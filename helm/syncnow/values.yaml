# Default values for syncnow
# This is a YAML-formatted file.

# Global configuration
global:
  imageRegistry: ""
  imagePullSecrets: []
  storageClass: ""

# Image configuration
image:
  registry: gcr.io
  repository: your-project/syncnow
  tag: "latest"
  pullPolicy: IfNotPresent

# Service account
serviceAccount:
  create: true
  annotations: {}
  name: ""

# Pod security context
podSecurityContext:
  fsGroup: 2000

# Container security context
securityContext:
  capabilities:
    drop:
    - ALL
  readOnlyRootFilesystem: true
  runAsNonRoot: true
  runAsUser: 1000

# SyncNow API Server configuration
server:
  enabled: true
  replicaCount: 2
  
  image:
    repository: syncnow-server
    tag: "latest"
    pullPolicy: IfNotPresent
  
  service:
    type: ClusterIP
    port: 8090
    targetPort: 8090
    annotations: {}
  
  ingress:
    enabled: false
    className: ""
    annotations: {}
    hosts:
      - host: syncnow-api.local
        paths:
          - path: /
            pathType: Prefix
    tls: []
  
  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 500m
      memory: 512Mi
  
  autoscaling:
    enabled: false
    minReplicas: 2
    maxReplicas: 10
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  
  nodeSelector: {}
  tolerations: []
  affinity: {}
  
  env:
    LOG_LEVEL: "info"
    SERVER_PORT: "8090"

  envFrom: []

  # Server-specific configuration
  auth:
    enabled: false
    defaultUser: "<EMAIL>"
    defaultTenant: "default"

  # Temporal configuration for server
  temporal:
    maxConcurrentActivityExecution: 500
    maxConcurrentWorkflowTaskExecution: 250
    maxConcurrentActivityTaskPollers: 10
    maxConcurrentWorkflowTaskPollers: 10
    workerActivitiesPerSecond: 500.0
    taskQueueActivitiesPerSecond: 500.0

  # Parallelism configuration for server
  parallelism:
    enablePartitioning: true
    txnBatchSize: 10000
    maxConcurrentWorkers: 8
    noPartitions: 8
    minRowsToSplit: 100000

  # Logging configuration for server
  logging:
    format: "json"

  # Monitoring configuration for server
  monitoring:
    metricsInterval: 30
    dashboard:
      enabled: true

  # Checkpoint configuration for server
  checkpoint:
    enabled: true
    interval: 60
    migrationName: "kubernetes-migration"
    autoRecover: true
    retainCount: 5
  
  livenessProbe:
    httpGet:
      path: /health
      port: 8090
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3
  
  readinessProbe:
    httpGet:
      path: /health
      port: 8090
    initialDelaySeconds: 5
    periodSeconds: 5
    timeoutSeconds: 3
    failureThreshold: 3

# SyncNow Worker configuration
worker:
  enabled: true
  replicaCount: 3
  
  image:
    repository: syncnow-worker
    tag: "latest"
    pullPolicy: IfNotPresent
  
  resources:
    limits:
      cpu: 2000m
      memory: 2Gi
    requests:
      cpu: 1000m
      memory: 1Gi
  
  autoscaling:
    enabled: true
    minReplicas: 3
    maxReplicas: 20
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80
  
  nodeSelector: {}
  tolerations: []
  affinity: {}
  
  env:
    LOG_LEVEL: "info"

  envFrom: []

  # Temporal configuration for worker
  temporal:
    maxConcurrentActivityExecution: 500
    maxConcurrentWorkflowTaskExecution: 250
    maxConcurrentActivityTaskPollers: 2
    maxConcurrentWorkflowTaskPollers: 2
    workerActivitiesPerSecond: 100.0
    taskQueueActivitiesPerSecond: 100.0

  # Parallelism configuration for worker
  parallelism:
    enablePartitioning: true
    txnBatchSize: 1000
    maxConcurrentWorkers: 8
    noPartitions: 8
    minRowsToSplit: 10000

  # Logging configuration for worker
  logging:
    format: "json"

  # Monitoring configuration for worker
  monitoring:
    metricsInterval: 30
    dashboard:
      enabled: false

  # Checkpoint configuration for worker
  checkpoint:
    enabled: true
    interval: 60
    migrationName: "kubernetes-migration"
    autoRecover: true
    retainCount: 5

# Web frontend configuration
web:
  enabled: true
  replicaCount: 2
  
  image:
    repository: syncnow-web
    tag: "latest"
    pullPolicy: IfNotPresent
  
  service:
    type: ClusterIP
    port: 3000
    targetPort: 3000
    annotations: {}
  
  ingress:
    enabled: true
    className: "nginx"
    annotations:
      nginx.ingress.kubernetes.io/rewrite-target: /
    hosts:
      - host: syncnow.local
        paths:
          - path: /
            pathType: Prefix
    tls: []
  
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 250m
      memory: 256Mi
  
  autoscaling:
    enabled: false
    minReplicas: 2
    maxReplicas: 5
    targetCPUUtilizationPercentage: 80
  
  nodeSelector: {}
  tolerations: []
  affinity: {}
  
  env:
    NODE_ENV: "production"
    NEXT_PUBLIC_API_URL: "http://syncnow-envoy:8080"
    NEXT_PUBLIC_GRPC_WEB_URL: "http://syncnow-envoy:8080"

# Envoy proxy configuration
envoy:
  enabled: true
  replicaCount: 2
  
  image:
    repository: envoyproxy/envoy
    tag: "v1.28-latest"
    pullPolicy: IfNotPresent
  
  service:
    type: ClusterIP
    port: 8080
    targetPort: 8080
    annotations: {}
  
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 250m
      memory: 256Mi
  
  nodeSelector: {}
  tolerations: []
  affinity: {}

# Temporal configuration
temporal:
  enabled: true

  # Task queue configuration
  taskQueue: "syncnow-task-queue"

  # Use external Temporal server or deploy one
  external:
    enabled: false
    host: "temporal.temporal.svc.cluster.local"
    port: 7233
    namespace: "default"
  
  # Internal Temporal deployment
  internal:
    enabled: true
    replicaCount: 1
    
    image:
      repository: temporalio/auto-setup
      tag: "1.22.0"
      pullPolicy: IfNotPresent
    
    service:
      type: ClusterIP
      port: 7233
      targetPort: 7233
      uiPort: 8080
      uiTargetPort: 8080
    
    resources:
      limits:
        cpu: 1000m
        memory: 1Gi
      requests:
        cpu: 500m
        memory: 512Mi
    
    env:
      DB: "postgresql"
      ENABLE_ES: "false"
      SKIP_SCHEMA_SETUP: "false"
    
    persistence:
      enabled: false
      storageClass: ""
      size: 10Gi

# PostgreSQL configuration (using Bitnami chart)
postgresql:
  enabled: true
  auth:
    postgresPassword: "postgres"
    username: "syncnow"
    password: "syncnow"
    database: "syncnow"
  primary:
    persistence:
      enabled: true
      size: 20Gi
    resources:
      limits:
        cpu: 1000m
        memory: 1Gi
      requests:
        cpu: 500m
        memory: 512Mi



# Configuration for external database (if not using PostgreSQL chart)
externalDatabase:
  enabled: false
  host: ""
  port: 5432
  database: "syncnow"
  username: "syncnow"
  password: ""
  existingSecret: ""
  existingSecretPasswordKey: ""



# Monitoring and observability
monitoring:
  enabled: false
  prometheus:
    enabled: false
    serviceMonitor:
      enabled: false
      namespace: ""
      labels: {}
  grafana:
    enabled: false
  jaeger:
    enabled: false

# Network policies
networkPolicy:
  enabled: false
  ingress: []
  egress: []

# Pod disruption budget
podDisruptionBudget:
  enabled: true
  minAvailable: 1
  # maxUnavailable: 1

# Secrets configuration
secrets:
  # JWT secret for authentication (change in production)
  jwtSecret: ""
  # Encryption key for sensitive data
  encryptionKey: ""
  # Additional secrets
  additional: {}
    # custom-secret: "value"

# Production-specific configurations
production:
  # Enable production optimizations
  enabled: false
  # Use production-grade resource limits
  useProductionResources: false
  # Enable security policies
  enableSecurityPolicies: false
