#!/bin/bash

# SyncNow IP-based Deployment Script
# This script deploys SyncNow using LoadBalancer services with external IPs

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Default values
NAMESPACE="default"
RELEASE_NAME="syncnow"
VALUES_FILE="values-ip-based.yaml"

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Deploy SyncNow using LoadBalancer services with external IPs

OPTIONS:
    -n, --namespace      Kubernetes namespace [default: default]
    -r, --release        Helm release name [default: syncnow]
    -f, --values-file    Values file path [default: values-ip-based.yaml]
    -h, --help           Show this help message

EXAMPLES:
    # Basic deployment
    $0

    # Custom namespace
    $0 -n syncnow-prod

    # Custom release name
    $0 -r my-syncnow

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -r|--release)
            RELEASE_NAME="$2"
            shift 2
            ;;
        -f|--values-file)
            VALUES_FILE="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

print_status "Starting SyncNow IP-based deployment..."
print_status "Namespace: $NAMESPACE"
print_status "Release: $RELEASE_NAME"
print_status "Values file: $VALUES_FILE"

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is not installed or not in PATH"
    exit 1
fi

# Check if helm is available
if ! command -v helm &> /dev/null; then
    print_error "helm is not installed or not in PATH"
    exit 1
fi

# Check if values file exists
if [[ ! -f "$VALUES_FILE" ]]; then
    print_error "Values file '$VALUES_FILE' not found"
    exit 1
fi

# Create namespace if it doesn't exist
print_status "Creating namespace if it doesn't exist..."
kubectl create namespace "$NAMESPACE" --dry-run=client -o yaml | kubectl apply -f -

# Update Helm dependencies
print_status "Updating Helm dependencies..."
helm dependency update

# Check if release already exists
if helm status "$RELEASE_NAME" -n "$NAMESPACE" &> /dev/null; then
    print_warning "Release '$RELEASE_NAME' already exists. Upgrading..."
    HELM_COMMAND="upgrade"
else
    print_status "Installing new release '$RELEASE_NAME'..."
    HELM_COMMAND="install"
fi

# Deploy with Helm
print_status "Deploying SyncNow..."
helm $HELM_COMMAND $RELEASE_NAME . -n $NAMESPACE -f $VALUES_FILE

# Wait for deployment to be ready
print_status "Waiting for deployments to be ready..."
kubectl wait --for=condition=available --timeout=300s deployment -l app.kubernetes.io/name=syncnow -n "$NAMESPACE"

# Wait for LoadBalancer services to get external IPs
print_status "Waiting for LoadBalancer services to get external IPs..."
print_status "This may take a few minutes..."

# Function to wait for external IP
wait_for_external_ip() {
    local service_name=$1
    local timeout=300
    local elapsed=0
    
    while [[ $elapsed -lt $timeout ]]; do
        external_ip=$(kubectl get svc "$service_name" -n "$NAMESPACE" -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "")
        if [[ -n "$external_ip" && "$external_ip" != "null" ]]; then
            echo "$external_ip"
            return 0
        fi
        sleep 10
        elapsed=$((elapsed + 10))
        echo -n "."
    done
    echo ""
    return 1
}

# Get external IPs
print_status "Getting external IP for Envoy service..."
ENVOY_IP=$(wait_for_external_ip "$RELEASE_NAME-envoy")
if [[ -n "$ENVOY_IP" ]]; then
    print_success "Envoy external IP: $ENVOY_IP"
else
    print_warning "Could not get Envoy external IP within timeout"
fi

print_status "Getting external IP for Web service..."
WEB_IP=$(wait_for_external_ip "$RELEASE_NAME-web")
if [[ -n "$WEB_IP" ]]; then
    print_success "Web external IP: $WEB_IP"
else
    print_warning "Could not get Web external IP within timeout"
fi

# Show deployment status
print_status "Deployment status:"
kubectl get pods -l app.kubernetes.io/name=syncnow -n "$NAMESPACE"

print_status "Services and their external IPs:"
kubectl get svc -l app.kubernetes.io/name=syncnow -n "$NAMESPACE"

print_success "SyncNow IP-based deployment completed!"

# Show access information
print_status "=== ACCESS INFORMATION ==="
if [[ -n "$WEB_IP" ]]; then
    print_success "🌐 Web Application: http://$WEB_IP:3000"
else
    print_warning "Web Application: Check 'kubectl get svc $RELEASE_NAME-web -n $NAMESPACE' for external IP"
fi

if [[ -n "$ENVOY_IP" ]]; then
    print_success "🔌 API Endpoint: http://$ENVOY_IP:8080"
    print_success "📡 gRPC-Web Endpoint: http://$ENVOY_IP:8080"
else
    print_warning "API Endpoint: Check 'kubectl get svc $RELEASE_NAME-envoy -n $NAMESPACE' for external IP"
fi

print_status "=== USEFUL COMMANDS ==="
print_status "Check service status:"
print_status "  kubectl get svc -l app.kubernetes.io/name=syncnow -n $NAMESPACE"
print_status ""
print_status "Check pod logs:"
print_status "  kubectl logs -l app.kubernetes.io/component=server -n $NAMESPACE"
print_status "  kubectl logs -l app.kubernetes.io/component=worker -n $NAMESPACE"
print_status "  kubectl logs -l app.kubernetes.io/component=web -n $NAMESPACE"
print_status ""
print_status "Scale deployments:"
print_status "  kubectl scale deployment $RELEASE_NAME-server --replicas=2 -n $NAMESPACE"
print_status "  kubectl scale deployment $RELEASE_NAME-worker --replicas=3 -n $NAMESPACE"

# Update web app environment variables if we got the Envoy IP
if [[ -n "$ENVOY_IP" ]]; then
    print_status "=== UPDATING WEB APP CONFIGURATION ==="
    print_status "Updating web app to use Envoy IP: $ENVOY_IP"
    
    # Update the deployment with the correct API URL
    kubectl patch deployment "$RELEASE_NAME-web" -n "$NAMESPACE" -p "{\"spec\":{\"template\":{\"spec\":{\"containers\":[{\"name\":\"web\",\"env\":[{\"name\":\"NEXT_PUBLIC_API_URL\",\"value\":\"http://$ENVOY_IP:8080\"},{\"name\":\"NEXT_PUBLIC_GRPC_WEB_URL\",\"value\":\"http://$ENVOY_IP:8080\"},{\"name\":\"NODE_ENV\",\"value\":\"production\"}]}]}}}}"
    
    print_success "Web app configuration updated!"
    print_status "Waiting for web app to restart..."
    kubectl rollout status deployment "$RELEASE_NAME-web" -n "$NAMESPACE"
fi

print_success "Deployment completed successfully!"
print_warning "Note: If you're using GKE, LoadBalancer services will incur additional costs."
print_status "For cost optimization, consider using NodePort services or port-forwarding for development."
