# SyncNow Configuration Management

This document explains how SyncNow server and worker configurations are managed in the Helm chart.

## 🏗️ Architecture Overview

SyncNow uses a **single binary** (`syncnow`) that can run in different modes:

- **Server Mode**: `syncnow start server --config=/path/to/server-config.yaml`
- **Worker Mode**: `syncnow start worker --config=/path/to/worker-config.yaml`

The Helm chart creates **separate ConfigMaps** for server and worker configurations, allowing fine-tuned control over each component.

## 📁 Configuration Files

### Server Configuration (`server-config.yaml`)

The server configuration includes:
- **HTTP/gRPC Server settings** (ports, authentication)
- **Database connection** settings
- **Temporal connection** settings
- **Worker capabilities** (for server-side workflow execution)
- **Monitoring and logging** settings

### Worker Configuration (`worker-config.yaml`)

The worker configuration includes:
- **Temporal connection** settings
- **Worker-specific parameters** (concurrency, polling)
- **Parallelism settings** for data processing
- **Monitoring and logging** settings

## 🔧 Helm Chart Implementation

### ConfigMap Structure

The Helm chart creates a single ConfigMap with both configurations:

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: syncnow-config
data:
  server-config.yaml: |
    # Server-specific configuration
    server:
      api_port: 8090
      grpc_port: 8090
      auth:
        enabled: false
    # ... rest of server config
  
  worker-config.yaml: |
    # Worker-specific configuration
    temporal:
      host_port: "temporal:7233"
      task_queue: "syncnow-task-queue"
    # ... rest of worker config
```

### Deployment Commands

**Server Deployment:**
```yaml
containers:
  - name: server
    command: ["./syncnow"]
    args: ["start", "server", "--config=/app/config/server-config.yaml"]
    volumeMounts:
      - name: config
        mountPath: /app/config
```

**Worker Deployment:**
```yaml
containers:
  - name: worker
    command: ["./syncnow"]
    args: ["start", "worker", "--config=/app/config/worker-config.yaml"]
    volumeMounts:
      - name: config
        mountPath: /app/config
```

## ⚙️ Configuration Parameters

### Server-Specific Parameters

```yaml
server:
  # Authentication settings
  auth:
    enabled: false
    defaultUser: "<EMAIL>"
    defaultTenant: "default"
  
  # Temporal settings for server
  temporal:
    maxConcurrentActivityExecution: 500
    maxConcurrentWorkflowTaskExecution: 250
    maxConcurrentActivityTaskPollers: 10
    maxConcurrentWorkflowTaskPollers: 10
    workerActivitiesPerSecond: 500.0
    taskQueueActivitiesPerSecond: 500.0
  
  # Parallelism settings for server
  parallelism:
    enablePartitioning: true
    txnBatchSize: 10000
    maxConcurrentWorkers: 8
    noPartitions: 8
    minRowsToSplit: 100000
```

### Worker-Specific Parameters

```yaml
worker:
  # Temporal settings for worker (typically lower concurrency)
  temporal:
    maxConcurrentActivityExecution: 500
    maxConcurrentWorkflowTaskExecution: 250
    maxConcurrentActivityTaskPollers: 2
    maxConcurrentWorkflowTaskPollers: 2
    workerActivitiesPerSecond: 100.0
    taskQueueActivitiesPerSecond: 100.0
  
  # Parallelism settings for worker
  parallelism:
    enablePartitioning: true
    txnBatchSize: 1000
    maxConcurrentWorkers: 8
    noPartitions: 8
    minRowsToSplit: 10000
```

### Shared Parameters

```yaml
# Temporal connection (shared by both)
temporal:
  enabled: true
  taskQueue: "syncnow-task-queue"
  external:
    enabled: true
    host: "temporal.temporal.svc.cluster.local"
    port: 7233
    namespace: "default"
```

## 🚀 Deployment Examples

### Basic Deployment

```bash
# Deploy with default configurations
helm install syncnow . -f values.yaml
```

### Production Deployment

```bash
# Deploy with production-optimized configurations
helm install syncnow . -f values-production.yaml
```

### Custom Configuration

```yaml
# custom-values.yaml
server:
  auth:
    enabled: true
    defaultUser: "<EMAIL>"
  temporal:
    maxConcurrentActivityExecution: 1000
    workerActivitiesPerSecond: 1000.0

worker:
  temporal:
    maxConcurrentActivityTaskPollers: 5
    workerActivitiesPerSecond: 200.0
  parallelism:
    txnBatchSize: 5000

temporal:
  taskQueue: "my-custom-task-queue"
```

```bash
helm install syncnow . -f custom-values.yaml
```

## 🔍 Environment Variables

Both server and worker pods receive these environment variables:

```yaml
env:
  # Database connection
  - name: DB_HOST
    value: "postgresql-service"
  - name: DB_PORT
    value: "5432"
  - name: DB_NAME
    value: "syncnow"
  - name: DB_USER
    value: "syncnow"
  - name: DB_PASSWORD
    valueFrom:
      secretKeyRef:
        name: syncnow-secret
        key: db-password
  
  # Temporal connection
  - name: TEMPORAL_HOST
    value: "temporal-service"
  - name: TEMPORAL_PORT
    value: "7233"
  - name: TEMPORAL_NAMESPACE
    value: "default"
  
  # Logging
  - name: LOG_LEVEL
    value: "info"
```

## 🛠️ Customization Examples

### High-Throughput Configuration

```yaml
server:
  temporal:
    maxConcurrentActivityExecution: 2000
    maxConcurrentWorkflowTaskExecution: 1000
    workerActivitiesPerSecond: 2000.0
  parallelism:
    txnBatchSize: 50000
    maxConcurrentWorkers: 16

worker:
  replicaCount: 10
  temporal:
    maxConcurrentActivityExecution: 1000
    workerActivitiesPerSecond: 500.0
  parallelism:
    txnBatchSize: 20000
    maxConcurrentWorkers: 12
```

### Memory-Optimized Configuration

```yaml
server:
  temporal:
    maxConcurrentActivityExecution: 100
    maxConcurrentWorkflowTaskExecution: 50
  parallelism:
    txnBatchSize: 1000
    maxConcurrentWorkers: 2

worker:
  temporal:
    maxConcurrentActivityExecution: 50
    maxConcurrentActivityTaskPollers: 1
  parallelism:
    txnBatchSize: 500
    maxConcurrentWorkers: 2
```

## 🔧 Troubleshooting

### Check Configuration

```bash
# View the generated ConfigMap
kubectl get configmap syncnow-config -o yaml

# Check server configuration
kubectl get configmap syncnow-config -o jsonpath='{.data.server-config\.yaml}'

# Check worker configuration
kubectl get configmap syncnow-config -o jsonpath='{.data.worker-config\.yaml}'
```

### Verify Pod Commands

```bash
# Check server pod command
kubectl get pod -l app.kubernetes.io/component=server -o jsonpath='{.items[0].spec.containers[0].command}'

# Check worker pod command
kubectl get pod -l app.kubernetes.io/component=worker -o jsonpath='{.items[0].spec.containers[0].command}'
```

### Update Configuration

```bash
# Update values and upgrade
helm upgrade syncnow . -f updated-values.yaml

# Force pod restart after config change
kubectl rollout restart deployment syncnow-server
kubectl rollout restart deployment syncnow-worker
```

This configuration management approach provides maximum flexibility while maintaining clear separation between server and worker concerns.
