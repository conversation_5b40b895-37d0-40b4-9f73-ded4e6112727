# IP-based deployment values for SyncNow Helm Chart
# This file is for internal deployments without domain names

# Global configuration
global:
  imageRegistry: "gcr.io"
  imagePullSecrets:
    - name: gcr-json-key
  storageClass: "standard"

# Image configuration
image:
  registry: gcr.io
  repository: your-gke-project/syncnow
  tag: "v1.0.0"
  pullPolicy: IfNotPresent

# Server configuration with LoadBalancer service
server:
  enabled: true
  replicaCount: 1
  
  image:
    repository: syncnow-server
    tag: "v1.0.0"
  
  service:
    type: LoadBalancer  # This will get an external IP
    port: 8090
    targetPort: 8090
    annotations:
      # For GKE, you can reserve a static IP
      # cloud.google.com/load-balancer-type: "External"
      # loadbalancer.openstack.org/floating-network-id: "your-network-id"  # For OpenStack
  
  # Disable ingress since we're using LoadBalancer
  ingress:
    enabled: false
  
  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 500m
      memory: 512Mi
  
  autoscaling:
    enabled: true
    minReplicas: 1
    maxReplicas: 5
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80
  
  env:
    LOG_LEVEL: "info"
    SERVER_PORT: "8090"

# Worker configuration
worker:
  enabled: true
  replicaCount: 1
  
  image:
    repository: syncnow-worker
    tag: "v1.0.0"
  
  resources:
    limits:
      cpu: 2000m
      memory: 2Gi
    requests:
      cpu: 1000m
      memory: 1Gi
  
  autoscaling:
    enabled: true
    minReplicas: 1
    maxReplicas: 10
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80
  
  env:
    LOG_LEVEL: "info"

# Web frontend configuration with LoadBalancer
web:
  enabled: true
  replicaCount: 1
  
  image:
    repository: syncnow-web
    tag: "v1.0.0"
  
  service:
    type: LoadBalancer  # This will get an external IP
    port: 3000
    targetPort: 3000
    annotations:
      # For GKE, you can reserve a static IP
      # cloud.google.com/load-balancer-type: "External"
  
  # Disable ingress since we're using LoadBalancer
  ingress:
    enabled: false
  
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 250m
      memory: 256Mi
  
  autoscaling:
    enabled: true
    minReplicas: 1
    maxReplicas: 3
    targetCPUUtilizationPercentage: 80
  
  env:
    NODE_ENV: "production"
    # These will be set to the LoadBalancer IPs after deployment
    NEXT_PUBLIC_API_URL: "http://ENVOY_EXTERNAL_IP:8080"
    NEXT_PUBLIC_GRPC_WEB_URL: "http://ENVOY_EXTERNAL_IP:8080"

# Envoy proxy configuration with LoadBalancer
envoy:
  enabled: true
  replicaCount: 1
  
  image:
    repository: envoyproxy/envoy
    tag: "v1.28-latest"
  
  service:
    type: LoadBalancer  # This will get an external IP
    port: 8080
    targetPort: 8080
    annotations:
      # For GKE, you can reserve a static IP
      # cloud.google.com/load-balancer-type: "External"
  
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 250m
      memory: 256Mi

# Use external Temporal (or disable if not needed)
temporal:
  enabled: true
  external:
    enabled: true
    host: "temporal.temporal.svc.cluster.local"
    port: 7233
    namespace: "default"
  internal:
    enabled: false

# Use built-in PostgreSQL for simplicity
postgresql:
  enabled: true
  auth:
    postgresPassword: "postgres"
    username: "syncnow"
    password: "syncnow"
    database: "syncnow"
  primary:
    persistence:
      enabled: true
      size: 10Gi
    resources:
      limits:
        cpu: 500m
        memory: 512Mi
      requests:
        cpu: 250m
        memory: 256Mi

# Use built-in Redis for simplicity
redis:
  enabled: true
  auth:
    enabled: false
  master:
    persistence:
      enabled: true
      size: 2Gi
    resources:
      limits:
        cpu: 250m
        memory: 256Mi
      requests:
        cpu: 100m
        memory: 128Mi

# Disable external database and Redis
externalDatabase:
  enabled: false

externalRedis:
  enabled: false

# Basic secrets (change in production)
secrets:
  jwtSecret: "your-jwt-secret-change-me"
  encryptionKey: "your-encryption-key-change-me"
  additional: {}

# Disable monitoring for simplicity
monitoring:
  enabled: false
  prometheus:
    enabled: false
  grafana:
    enabled: false
  jaeger:
    enabled: false

# Disable network policies for internal use
networkPolicy:
  enabled: false

# Pod disruption budget
podDisruptionBudget:
  enabled: true
  minAvailable: 1

# Production configurations
production:
  enabled: false
  useProductionResources: false
  enableSecurityPolicies: false

# Security context (less restrictive for internal use)
podSecurityContext:
  fsGroup: 2000

securityContext:
  capabilities:
    drop:
    - ALL
  readOnlyRootFilesystem: true
  runAsNonRoot: true
  runAsUser: 1000
