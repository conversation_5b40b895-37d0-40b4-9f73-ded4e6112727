{{- if .Values.envoy.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "syncnow.fullname" . }}-envoy
  labels:
    {{- include "syncnow.labels" . | nindent 4 }}
    app.kubernetes.io/component: envoy
  {{- with .Values.envoy.service.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.envoy.service.type }}
  ports:
    - port: {{ .Values.envoy.service.port }}
      targetPort: {{ .Values.envoy.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "syncnow.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: envoy
{{- end }}
