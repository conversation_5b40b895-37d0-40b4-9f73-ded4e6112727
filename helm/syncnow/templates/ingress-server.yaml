{{- if and .Values.server.enabled .Values.server.ingress.enabled }}
{{- $fullName := include "syncnow.fullname" . }}
{{- $svcPort := .Values.server.service.port }}
{{- if and .Values.server.ingress.className (not (hasKey .Values.server.ingress.annotations "kubernetes.io/ingress.class")) }}
  {{- $_ := set .Values.server.ingress.annotations "kubernetes.io/ingress.class" .Values.server.ingress.className}}
{{- end }}
{{- if semverCompare ">=1.19-0" .Capabilities.KubeVersion.GitVersion }}
apiVersion: networking.k8s.io/v1
{{- else if semverCompare ">=1.14-0" .Capabilities.KubeVersion.GitVersion }}
apiVersion: networking.k8s.io/v1beta1
{{- else }}
apiVersion: extensions/v1beta1
{{- end }}
kind: Ingress
metadata:
  name: {{ $fullName }}-server
  labels:
    {{- include "syncnow.labels" . | nindent 4 }}
    app.kubernetes.io/component: server
  {{- with .Values.server.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if and .Values.server.ingress.className (semverCompare ">=1.18-0" .Capabilities.KubeVersion.GitVersion) }}
  ingressClassName: {{ .Values.server.ingress.className }}
  {{- end }}
  {{- if .Values.server.ingress.tls }}
  tls:
    {{- range .Values.server.ingress.tls }}
    - hosts:
        {{- range .hosts }}
        - {{ . | quote }}
        {{- end }}
      secretName: {{ .secretName }}
    {{- end }}
  {{- end }}
  rules:
    {{- range .Values.server.ingress.hosts }}
    - host: {{ .host | quote }}
      http:
        paths:
          {{- range .paths }}
          - path: {{ .path }}
            {{- if and .pathType (semverCompare ">=1.18-0" $.Capabilities.KubeVersion.GitVersion) }}
            pathType: {{ .pathType }}
            {{- end }}
            backend:
              {{- if semverCompare ">=1.19-0" $.Capabilities.KubeVersion.GitVersion }}
              service:
                name: {{ $fullName }}-envoy
                port:
                  number: {{ $.Values.envoy.service.port }}
              {{- else }}
              serviceName: {{ $fullName }}-envoy
              servicePort: {{ $.Values.envoy.service.port }}
              {{- end }}
          {{- end }}
    {{- end }}
{{- end }}
