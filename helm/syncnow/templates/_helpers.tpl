{{/*
Expand the name of the chart.
*/}}
{{- define "syncnow.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "syncnow.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "syncnow.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "syncnow.labels" -}}
helm.sh/chart: {{ include "syncnow.chart" . }}
{{ include "syncnow.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "syncnow.selectorLabels" -}}
app.kubernetes.io/name: {{ include "syncnow.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "syncnow.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "syncnow.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{/*
Database host
*/}}
{{- define "syncnow.databaseHost" -}}
{{- if .Values.postgresql.enabled }}
{{- printf "%s-postgresql" (include "syncnow.fullname" .) }}
{{- else if .Values.externalDatabase.enabled }}
{{- .Values.externalDatabase.host }}
{{- else }}
{{- fail "Either postgresql.enabled or externalDatabase.enabled must be true" }}
{{- end }}
{{- end }}

{{/*
Database port
*/}}
{{- define "syncnow.databasePort" -}}
{{- if .Values.postgresql.enabled }}
{{- "5432" }}
{{- else if .Values.externalDatabase.enabled }}
{{- .Values.externalDatabase.port | toString }}
{{- else }}
{{- "5432" }}
{{- end }}
{{- end }}

{{/*
Database name
*/}}
{{- define "syncnow.databaseName" -}}
{{- if .Values.postgresql.enabled }}
{{- .Values.postgresql.auth.database }}
{{- else if .Values.externalDatabase.enabled }}
{{- .Values.externalDatabase.database }}
{{- else }}
{{- "syncnow" }}
{{- end }}
{{- end }}

{{/*
Database username
*/}}
{{- define "syncnow.databaseUsername" -}}
{{- if .Values.postgresql.enabled }}
{{- .Values.postgresql.auth.username }}
{{- else if .Values.externalDatabase.enabled }}
{{- .Values.externalDatabase.username }}
{{- else }}
{{- "syncnow" }}
{{- end }}
{{- end }}

{{/*
Database secret name
*/}}
{{- define "syncnow.databaseSecretName" -}}
{{- if .Values.postgresql.enabled }}
{{- printf "%s-postgresql" (include "syncnow.fullname" .) }}
{{- else if .Values.externalDatabase.existingSecret }}
{{- .Values.externalDatabase.existingSecret }}
{{- else }}
{{- printf "%s-database" (include "syncnow.fullname" .) }}
{{- end }}
{{- end }}

{{/*
Database secret password key
*/}}
{{- define "syncnow.databaseSecretPasswordKey" -}}
{{- if .Values.postgresql.enabled }}
{{- "password" }}
{{- else if .Values.externalDatabase.existingSecretPasswordKey }}
{{- .Values.externalDatabase.existingSecretPasswordKey }}
{{- else }}
{{- "password" }}
{{- end }}
{{- end }}

{{/*
Redis host
*/}}
{{- define "syncnow.redisHost" -}}
{{- if .Values.redis.enabled }}
{{- printf "%s-redis-master" (include "syncnow.fullname" .) }}
{{- else if .Values.externalRedis.enabled }}
{{- .Values.externalRedis.host }}
{{- else }}
{{- fail "Either redis.enabled or externalRedis.enabled must be true" }}
{{- end }}
{{- end }}

{{/*
Redis port
*/}}
{{- define "syncnow.redisPort" -}}
{{- if .Values.redis.enabled }}
{{- "6379" }}
{{- else if .Values.externalRedis.enabled }}
{{- .Values.externalRedis.port | toString }}
{{- else }}
{{- "6379" }}
{{- end }}
{{- end }}

{{/*
Redis secret name
*/}}
{{- define "syncnow.redisSecretName" -}}
{{- if .Values.redis.enabled }}
{{- printf "%s-redis" (include "syncnow.fullname" .) }}
{{- else if .Values.externalRedis.existingSecret }}
{{- .Values.externalRedis.existingSecret }}
{{- else }}
{{- printf "%s-redis" (include "syncnow.fullname" .) }}
{{- end }}
{{- end }}

{{/*
Redis secret password key
*/}}
{{- define "syncnow.redisSecretPasswordKey" -}}
{{- if .Values.redis.enabled }}
{{- "redis-password" }}
{{- else if .Values.externalRedis.existingSecretPasswordKey }}
{{- .Values.externalRedis.existingSecretPasswordKey }}
{{- else }}
{{- "password" }}
{{- end }}
{{- end }}

{{/*
Temporal host
*/}}
{{- define "syncnow.temporalHost" -}}
{{- if .Values.temporal.internal.enabled }}
{{- printf "%s-temporal" (include "syncnow.fullname" .) }}
{{- else if .Values.temporal.external.enabled }}
{{- .Values.temporal.external.host }}
{{- else }}
{{- fail "Either temporal.internal.enabled or temporal.external.enabled must be true" }}
{{- end }}
{{- end }}

{{/*
Temporal port
*/}}
{{- define "syncnow.temporalPort" -}}
{{- if .Values.temporal.internal.enabled }}
{{- "7233" }}
{{- else if .Values.temporal.external.enabled }}
{{- .Values.temporal.external.port | toString }}
{{- else }}
{{- "7233" }}
{{- end }}
{{- end }}

{{/*
Database password
*/}}
{{- define "syncnow.databasePassword" -}}
{{- if .Values.postgresql.enabled }}
{{- .Values.postgresql.auth.password }}
{{- else if .Values.externalDatabase.enabled }}
{{- .Values.externalDatabase.password }}
{{- else }}
{{- "postgres" }}
{{- end }}
{{- end }}

{{/*
Redis password
*/}}
{{- define "syncnow.redisPassword" -}}
{{- if .Values.redis.enabled }}
{{- .Values.redis.auth.password | default "" }}
{{- else if .Values.externalRedis.enabled }}
{{- .Values.externalRedis.password }}
{{- else }}
{{- "" }}
{{- end }}
{{- end }}
