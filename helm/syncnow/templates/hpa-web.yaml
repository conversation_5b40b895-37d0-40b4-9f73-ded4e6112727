{{- if and .Values.web.enabled .Values.web.autoscaling.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "syncnow.fullname" . }}-web
  labels:
    {{- include "syncnow.labels" . | nindent 4 }}
    app.kubernetes.io/component: web
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "syncnow.fullname" . }}-web
  minReplicas: {{ .Values.web.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.web.autoscaling.maxReplicas }}
  metrics:
    {{- if .Values.web.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ .Values.web.autoscaling.targetCPUUtilizationPercentage }}
    {{- end }}
    {{- if .Values.web.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ .Values.web.autoscaling.targetMemoryUtilizationPercentage }}
    {{- end }}
{{- end }}
