{{- if and .Values.temporal.enabled .Values.temporal.internal.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "syncnow.fullname" . }}-temporal
  labels:
    {{- include "syncnow.labels" . | nindent 4 }}
    app.kubernetes.io/component: temporal
spec:
  type: {{ .Values.temporal.internal.service.type }}
  ports:
    - port: {{ .Values.temporal.internal.service.port }}
      targetPort: {{ .Values.temporal.internal.service.port }}
      protocol: TCP
      name: rpc
    - port: {{ .Values.temporal.internal.service.uiPort }}
      targetPort: {{ .Values.temporal.internal.service.uiTargetPort }}
      protocol: TCP
      name: ui
  selector:
    {{- include "syncnow.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: temporal
{{- end }}
