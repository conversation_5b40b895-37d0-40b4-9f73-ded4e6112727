apiVersion: v1
kind: Secret
metadata:
  name: {{ include "syncnow.fullname" . }}-secret
  labels:
    {{- include "syncnow.labels" . | nindent 4 }}
type: Opaque
data:
  {{- if not .Values.externalDatabase.enabled }}
  db-password: {{ include "syncnow.databasePassword" . | b64enc | quote }}
  {{- end }}
  jwt-secret: {{ .Values.secrets.jwtSecret | default "your-default-jwt-secret-change-in-production" | b64enc | quote }}
  {{- if .Values.secrets.encryptionKey }}
  encryption-key: {{ .Values.secrets.encryptionKey | b64enc | quote }}
  {{- end }}
  {{- range $key, $value := .Values.secrets.additional }}
  {{ $key }}: {{ $value | b64enc | quote }}
  {{- end }}
