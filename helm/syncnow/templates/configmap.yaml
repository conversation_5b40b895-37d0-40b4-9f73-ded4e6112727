apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "syncnow.fullname" . }}-config
  labels:
    {{- include "syncnow.labels" . | nindent 4 }}
data:
  server-config.yaml: |
    # SyncNow Server Configuration for Kubernetes

    # HTTP/gRPC Server configuration
    server:
      api_port: {{ .Values.server.service.targetPort }}
      grpc_port: {{ .Values.server.service.targetPort }}
      auth:
        enabled: {{ .Values.server.auth.enabled | default false }}
        default_user: "{{ .Values.server.auth.defaultUser | default "<EMAIL>" }}"
        default_tenant: "{{ .Values.server.auth.defaultTenant | default "default" }}"

    # Database configuration
    database:
      db_host: "${DB_HOST}"
      db_port: "${DB_PORT}"
      db_name: "${DB_NAME}"
      db_user: "${DB_USER}"
      db_password: "${DB_PASSWORD}"
      db_sslmode: "${DB_SSLMODE}"

    # Temporal server connection details and worker settings
    temporal:
      host_port: "${TEMPORAL_HOST}:${TEMPORAL_PORT}"
      namespace: "${TEMPORAL_NAMESPACE}"
      task_queue: "{{ .Values.temporal.taskQueue | default "syncnow-task-queue" }}"
      max_concurrent_activity_execution: {{ .Values.server.temporal.maxConcurrentActivityExecution | default 500 }}
      max_concurrent_workflow_task_execution: {{ .Values.server.temporal.maxConcurrentWorkflowTaskExecution | default 250 }}
      max_concurrent_activity_task_pollers: {{ .Values.server.temporal.maxConcurrentActivityTaskPollers | default 10 }}
      max_concurrent_workflow_task_pollers: {{ .Values.server.temporal.maxConcurrentWorkflowTaskPollers | default 10 }}
      worker_activities_per_second: {{ .Values.server.temporal.workerActivitiesPerSecond | default 500.0 }}
      task_queue_activities_per_second: {{ .Values.server.temporal.taskQueueActivitiesPerSecond | default 500.0 }}

    # Parallelism settings used by activities run by this worker
    parallelism:
      enable_partitioning: {{ .Values.server.parallelism.enablePartitioning | default true }}
      txn_batch_size: {{ .Values.server.parallelism.txnBatchSize | default 10000 }}
      max_concurrent_workers: {{ .Values.server.parallelism.maxConcurrentWorkers | default 8 }}
      no_partitions: {{ .Values.server.parallelism.noPartitions | default 8 }}
      min_rows_to_split: {{ .Values.server.parallelism.minRowsToSplit | default 100000 }}

    # Logging configuration
    logging:
      level: "${LOG_LEVEL}"
      format: "{{ .Values.server.logging.format | default "json" }}"
      output_dir: "/app/logs"

    # Monitoring configuration
    monitoring:
      enabled: {{ .Values.monitoring.enabled | default false }}
      metrics_interval: {{ .Values.server.monitoring.metricsInterval | default 30 }}
      output_dir: "/app/metrics"
      dashboard:
        enabled: {{ .Values.server.monitoring.dashboard.enabled | default true }}
        port: {{ .Values.server.service.targetPort }}

    # Checkpoint configuration
    checkpoint:
      enabled: {{ .Values.server.checkpoint.enabled | default true }}
      directory: "/app/data/checkpoints"
      interval: {{ .Values.server.checkpoint.interval | default 60 }}
      migration_name: "{{ .Values.server.checkpoint.migrationName | default "kubernetes-migration" }}"
      auto_recover: {{ .Values.server.checkpoint.autoRecover | default true }}
      retain_count: {{ .Values.server.checkpoint.retainCount | default 5 }}

  worker-config.yaml: |
    # SyncNow Worker Configuration for Kubernetes

    # Temporal server connection details and worker settings
    temporal:
      host_port: "${TEMPORAL_HOST}:${TEMPORAL_PORT}"
      namespace: "${TEMPORAL_NAMESPACE}"
      task_queue: "{{ .Values.temporal.taskQueue | default "syncnow-task-queue" }}"
      max_concurrent_activity_execution: {{ .Values.worker.temporal.maxConcurrentActivityExecution | default 500 }}
      max_concurrent_workflow_task_execution: {{ .Values.worker.temporal.maxConcurrentWorkflowTaskExecution | default 250 }}
      max_concurrent_activity_task_pollers: {{ .Values.worker.temporal.maxConcurrentActivityTaskPollers | default 2 }}
      max_concurrent_workflow_task_pollers: {{ .Values.worker.temporal.maxConcurrentWorkflowTaskPollers | default 2 }}
      worker_activities_per_second: {{ .Values.worker.temporal.workerActivitiesPerSecond | default 100.0 }}
      task_queue_activities_per_second: {{ .Values.worker.temporal.taskQueueActivitiesPerSecond | default 100.0 }}

    # Parallelism settings used by activities run by this worker
    parallelism:
      enable_partitioning: {{ .Values.worker.parallelism.enablePartitioning | default true }}
      txn_batch_size: {{ .Values.worker.parallelism.txnBatchSize | default 1000 }}
      max_concurrent_workers: {{ .Values.worker.parallelism.maxConcurrentWorkers | default 8 }}
      no_partitions: {{ .Values.worker.parallelism.noPartitions | default 8 }}
      min_rows_to_split: {{ .Values.worker.parallelism.minRowsToSplit | default 10000 }}

    # Logging configuration
    logging:
      level: "${LOG_LEVEL}"
      format: "{{ .Values.worker.logging.format | default "json" }}"
      output_dir: "/app/logs"

    # Monitoring configuration
    monitoring:
      enabled: {{ .Values.monitoring.enabled | default false }}
      metrics_interval: {{ .Values.worker.monitoring.metricsInterval | default 30 }}
      output_dir: "/app/metrics"
      dashboard:
        enabled: {{ .Values.worker.monitoring.dashboard.enabled | default false }}
        port: 8080

    # Checkpoint configuration
    checkpoint:
      enabled: {{ .Values.worker.checkpoint.enabled | default true }}
      directory: "/app/data/checkpoints"
      interval: {{ .Values.worker.checkpoint.interval | default 60 }}
      migration_name: "{{ .Values.worker.checkpoint.migrationName | default "kubernetes-migration" }}"
      auto_recover: {{ .Values.worker.checkpoint.autoRecover | default true }}
      retain_count: {{ .Values.worker.checkpoint.retainCount | default 5 }}

  envoy.yaml: |
    admin:
      address:
        socket_address: { address: 0.0.0.0, port_value: 9901 }

    static_resources:
      listeners:
      - name: listener_0
        address:
          socket_address: { address: 0.0.0.0, port_value: 8080 }
        filter_chains:
        - filters:
          - name: envoy.filters.network.http_connection_manager
            typed_config:
              "@type": type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
              codec_type: auto
              stat_prefix: ingress_http
              route_config:
                name: local_route
                virtual_hosts:
                - name: local_service
                  domains: ["*"]
                  routes:
                  - match: { prefix: "/" }
                    route:
                      cluster: syncnow_service
                      timeout: 0s
                      max_stream_duration:
                        grpc_timeout_header_max: 0s
                  cors:
                    allow_origin_string_match:
                    - prefix: "*"
                    allow_methods: GET, PUT, DELETE, POST, OPTIONS
                    allow_headers: keep-alive,user-agent,cache-control,content-type,content-transfer-encoding,custom-header-1,x-accept-content-transfer-encoding,x-accept-response-streaming,x-user-agent,x-grpc-web,grpc-timeout,authorization
                    max_age: "1728000"
                    expose_headers: custom-header-1,grpc-status,grpc-message
              http_filters:
              - name: envoy.filters.http.grpc_web
                typed_config:
                  "@type": type.googleapis.com/envoy.extensions.filters.http.grpc_web.v3.GrpcWeb
              - name: envoy.filters.http.cors
                typed_config:
                  "@type": type.googleapis.com/envoy.extensions.filters.http.cors.v3.Cors
              - name: envoy.filters.http.router
                typed_config:
                  "@type": type.googleapis.com/envoy.extensions.filters.http.router.v3.Router

      clusters:
      - name: syncnow_service
        connect_timeout: 0.25s
        type: logical_dns
        http2_protocol_options: {}
        lb_policy: round_robin
        load_assignment:
          cluster_name: syncnow_service
          endpoints:
          - lb_endpoints:
            - endpoint:
                address:
                  socket_address:
                    address: {{ include "syncnow.fullname" . }}-server
                    port_value: {{ .Values.server.service.port }}
