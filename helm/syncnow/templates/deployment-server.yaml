{{- if .Values.server.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "syncnow.fullname" . }}-server
  labels:
    {{- include "syncnow.labels" . | nindent 4 }}
    app.kubernetes.io/component: server
spec:
  {{- if not .Values.server.autoscaling.enabled }}
  replicas: {{ .Values.server.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "syncnow.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: server
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
      labels:
        {{- include "syncnow.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: server
    spec:
      {{- with .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "syncnow.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: server
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.registry }}/{{ .Values.server.image.repository }}:{{ .Values.server.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.server.image.pullPolicy }}
          command: ["./syncnow"]
          args: ["start", "server", "--config", "/app/config/server-config.yaml"]
          ports:
            - name: http
              containerPort: {{ .Values.server.service.targetPort }}
              protocol: TCP
            - name: grpc
              containerPort: {{ .Values.server.service.targetPort }}
              protocol: TCP
          env:
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: {{ include "syncnow.fullname" . }}-secret
                  key: jwt-secret
            - name: DB_HOST
              value: {{ include "syncnow.databaseHost" . | quote }}
            - name: DB_PORT
              value: {{ include "syncnow.databasePort" . | quote }}
            - name: DB_NAME
              value: {{ include "syncnow.databaseName" . | quote }}
            - name: DB_USER
              value: {{ include "syncnow.databaseUsername" . | quote }}
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "syncnow.databaseSecretName" . }}
                  key: {{ include "syncnow.databaseSecretPasswordKey" . }}
            - name: DB_SSLMODE
              value: "require"
            - name: TEMPORAL_HOST
              value: {{ include "syncnow.temporalHost" . | quote }}
            - name: TEMPORAL_PORT
              value: {{ include "syncnow.temporalPort" . | quote }}
            - name: TEMPORAL_NAMESPACE
              value: "default"
            - name: REDIS_HOST
              value: {{ include "syncnow.redisHost" . | quote }}
            - name: REDIS_PORT
              value: {{ include "syncnow.redisPort" . | quote }}
            {{- if .Values.redis.auth.enabled }}
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "syncnow.redisSecretName" . }}
                  key: {{ include "syncnow.redisSecretPasswordKey" . }}
            {{- end }}
            {{- range $key, $value := .Values.server.env }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
          {{- with .Values.server.envFrom }}
          envFrom:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          livenessProbe:
            {{- toYaml .Values.server.livenessProbe | nindent 12 }}
          readinessProbe:
            {{- toYaml .Values.server.readinessProbe | nindent 12 }}
          resources:
            {{- toYaml .Values.server.resources | nindent 12 }}
          volumeMounts:
            - name: config
              mountPath: /app/config
              readOnly: true
            - name: logs
              mountPath: /app/logs
            - name: data
              mountPath: /app/data
      volumes:
        - name: config
          configMap:
            name: {{ include "syncnow.fullname" . }}-config
        - name: logs
          emptyDir: {}
        - name: data
          emptyDir: {}
      {{- with .Values.server.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.server.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.server.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
