{{- if .Values.web.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "syncnow.fullname" . }}-web
  labels:
    {{- include "syncnow.labels" . | nindent 4 }}
    app.kubernetes.io/component: web
spec:
  {{- if not .Values.web.autoscaling.enabled }}
  replicas: {{ .Values.web.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "syncnow.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: web
  template:
    metadata:
      labels:
        {{- include "syncnow.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: web
    spec:
      {{- with .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "syncnow.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: web
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.registry }}/{{ .Values.web.image.repository }}:{{ .Values.web.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.web.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.web.service.targetPort }}
              protocol: TCP
          env:
            {{- range $key, $value := .Values.web.env }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
          livenessProbe:
            httpGet:
              path: /
              port: {{ .Values.web.service.targetPort }}
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /
              port: {{ .Values.web.service.targetPort }}
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
            failureThreshold: 3
          resources:
            {{- toYaml .Values.web.resources | nindent 12 }}
      {{- with .Values.web.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.web.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.web.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
