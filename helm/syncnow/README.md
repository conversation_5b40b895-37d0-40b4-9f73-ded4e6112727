# SyncNow Helm Chart

A comprehensive Helm chart for deploying SyncNow - Database Migration and Synchronization Platform on Kubernetes, specifically optimized for Google Kubernetes Engine (GKE).

## Overview

This Helm chart deploys a complete SyncNow stack including:

- **SyncNow gRPC Server**: Core API server handling database synchronization operations
- **SyncNow Worker**: Temporal workers executing synchronization workflows
- **SyncNow Web App**: Next.js frontend application
- **Envoy Proxy**: gRPC-Web proxy for browser compatibility
- **PostgreSQL**: Database backend (optional, can use external Cloud SQL)
- **Redis**: Caching and session storage (optional, can use external Memorystore)
- **Temporal**: Workflow orchestration (optional, can use external managed Temporal)

## Prerequisites

- Kubernetes 1.19+
- Helm 3.8+
- PV provisioner support in the underlying infrastructure
- LoadBalancer support (for GKE)

### For GKE Production Deployment

- Google Cloud SDK configured
- GKE cluster with Workload Identity enabled
- Cloud SQL instance (recommended for production)
- Memorystore Redis instance (recommended for production)
- Container Registry or Artifact Registry access
- SSL certificates configured

## Installation

### Quick Start (Development)

```bash
# Add the chart repository (if published)
helm repo add syncnow https://your-helm-repo.com
helm repo update

# Install with default values
helm install syncnow syncnow/syncnow
```

### Local Development

```bash
# Clone the repository
git clone https://github.com/virajago/syncnow.git
cd syncnow/helm/syncnow

# Install dependencies
helm dependency update

# Install the chart
helm install syncnow . -f values.yaml
```

### Production Deployment on GKE

```bash
# 1. Prepare your production values
cp values-production.yaml my-production-values.yaml

# 2. Update the values with your specific configuration
# Edit my-production-values.yaml with your:
# - Domain names
# - Image repositories
# - Database connection details
# - Redis connection details
# - SSL certificate names

# 3. Create necessary secrets
kubectl create secret generic syncnow-secrets \
  --from-literal=jwt-secret="your-jwt-secret" \
  --from-literal=encryption-key="your-encryption-key"

kubectl create secret generic cloudsql-db-credentials \
  --from-literal=password="your-db-password"

# 4. Install the chart
helm install syncnow . -f my-production-values.yaml
```

## Configuration

### Key Configuration Sections

#### Server Configuration
```yaml
server:
  enabled: true
  replicaCount: 3
  image:
    repository: syncnow-server
    tag: "v1.0.0"
  resources:
    limits:
      cpu: 2000m
      memory: 2Gi
    requests:
      cpu: 1000m
      memory: 1Gi
```

#### Worker Configuration
```yaml
worker:
  enabled: true
  replicaCount: 5
  autoscaling:
    enabled: true
    minReplicas: 5
    maxReplicas: 50
```

#### Database Configuration
```yaml
# For managed Cloud SQL
postgresql:
  enabled: false

externalDatabase:
  enabled: true
  host: "your-cloud-sql-proxy-service"
  database: "syncnow"
  username: "syncnow"
  existingSecret: "cloudsql-db-credentials"
```

### Environment-Specific Values

- `values.yaml`: Default development configuration
- `values-production.yaml`: Production-ready configuration for GKE

## Components

### SyncNow Server
- Handles gRPC API requests
- Manages database connections and synchronization
- Provides health checks and metrics

### SyncNow Worker
- Executes Temporal workflows
- Handles long-running synchronization tasks
- Auto-scales based on workload

### Web Application
- Next.js frontend
- Communicates with server via gRPC-Web
- Responsive UI for managing synchronizations

### Envoy Proxy
- Translates gRPC-Web to gRPC
- Handles CORS and routing
- Load balances requests to server instances

## Monitoring and Observability

The chart includes optional monitoring components:

```yaml
monitoring:
  enabled: true
  prometheus:
    enabled: true
    serviceMonitor:
      enabled: true
  grafana:
    enabled: true
  jaeger:
    enabled: true
```

## Security

### Production Security Features

- Non-root containers
- Read-only root filesystems
- Security contexts with dropped capabilities
- Network policies
- Secret management
- Pod disruption budgets

### Secrets Management

The chart supports multiple secret management approaches:

1. **Kubernetes Secrets** (default)
2. **External Secret Operator**
3. **Google Secret Manager** (for GKE)

## Scaling

### Horizontal Pod Autoscaling

All components support HPA:

```yaml
autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 20
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80
```

### Vertical Scaling

Adjust resource requests and limits based on your workload:

```yaml
resources:
  limits:
    cpu: 4000m
    memory: 4Gi
  requests:
    cpu: 2000m
    memory: 2Gi
```

## Troubleshooting

### Common Issues

1. **Pod startup failures**: Check resource limits and node capacity
2. **Database connection issues**: Verify database credentials and network policies
3. **gRPC-Web connectivity**: Ensure Envoy proxy is running and properly configured
4. **Temporal workflow failures**: Check Temporal server connectivity and namespace configuration

### Debugging Commands

```bash
# Check pod status
kubectl get pods -l app.kubernetes.io/name=syncnow

# View logs
kubectl logs -l app.kubernetes.io/component=server
kubectl logs -l app.kubernetes.io/component=worker

# Check services
kubectl get svc -l app.kubernetes.io/name=syncnow

# Describe problematic pods
kubectl describe pod <pod-name>
```

## Upgrading

```bash
# Update dependencies
helm dependency update

# Upgrade the release
helm upgrade syncnow . -f my-production-values.yaml

# Check upgrade status
helm status syncnow
```

## Uninstalling

```bash
# Uninstall the release
helm uninstall syncnow

# Clean up PVCs (if needed)
kubectl delete pvc -l app.kubernetes.io/name=syncnow
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test with `helm lint` and `helm template`
5. Submit a pull request

## GKE-Specific Deployment Guide

### Prerequisites for GKE

1. **Create GKE Cluster**:
```bash
gcloud container clusters create syncnow-cluster \
  --zone=us-central1-a \
  --num-nodes=3 \
  --enable-autoscaling \
  --min-nodes=3 \
  --max-nodes=10 \
  --enable-autorepair \
  --enable-autoupgrade \
  --workload-pool=PROJECT_ID.svc.id.goog
```

2. **Set up Cloud SQL**:
```bash
gcloud sql instances create syncnow-db \
  --database-version=POSTGRES_14 \
  --tier=db-custom-2-4096 \
  --region=us-central1
```

3. **Set up Memorystore Redis**:
```bash
gcloud redis instances create syncnow-redis \
  --size=1 \
  --region=us-central1 \
  --redis-version=redis_6_x
```

4. **Configure Static IPs**:
```bash
gcloud compute addresses create syncnow-api-ip --global
gcloud compute addresses create syncnow-web-ip --global
```

5. **Set up SSL Certificates**:
```bash
kubectl apply -f - <<EOF
apiVersion: networking.gke.io/v1
kind: ManagedCertificate
metadata:
  name: syncnow-api-ssl-cert
spec:
  domains:
    - api.syncnow.yourdomain.com
---
apiVersion: networking.gke.io/v1
kind: ManagedCertificate
metadata:
  name: syncnow-web-ssl-cert
spec:
  domains:
    - app.syncnow.yourdomain.com
EOF
```

### Production Deployment Steps

1. **Build and Push Images**:
```bash
# Build server image
docker build -t gcr.io/PROJECT_ID/syncnow-server:v1.0.0 .
docker push gcr.io/PROJECT_ID/syncnow-server:v1.0.0

# Build web image
docker build -t gcr.io/PROJECT_ID/syncnow-web:v1.0.0 ./web
docker push gcr.io/PROJECT_ID/syncnow-web:v1.0.0
```

2. **Deploy with Helm**:
```bash
helm install syncnow . -f values-production.yaml \
  --set image.registry=gcr.io/PROJECT_ID \
  --set externalDatabase.host=CLOUD_SQL_PROXY_SERVICE \
  --set externalRedis.host=REDIS_IP_ADDRESS
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.
