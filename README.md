# SyncNow

A powerful tool for replicating database schemas and data from various source databases to target databases.

## Table of Contents

- [Overview](#overview)
- [Quick Start](#quick-start)
- [Web Application Setup](#web-application-setup)
  - [Prerequisites](#prerequisites)
  - [Development Environment](#development-environment)
  - [Production Deployment](#production-deployment)
- [CLI Tool Setup](#cli-tool-setup)
  - [Building from Source](#building-from-source)
  - [Running with Temporal](#running-with-temporal)
  - [Configuration](#configuration)
- [SyncNow CLI Commands](#syncnow-cli-commands)
  - [Global Flags](#global-flags)
  - [Core Commands](#core-commands)
  - [Database Management Commands](#database-management-commands)
  - [Resource Creation Commands](#resource-creation-commands)
  - [Common Usage Patterns](#common-usage-patterns)
- [Testing](#testing)
  - [Quick Test Run](#quick-test-run)
  - [Database Schema Testing](#database-schema-testing)
  - [Test Environment Setup](#test-environment-setup)
- [Development Guide](#development-guide)
  - [Project Structure](#project-structure)
  - [Development Commands](#development-commands)
  - [Contributing](#contributing)
- [Architecture & Features](#architecture--features)
  - [Unified Metrics System](#unified-metrics-system)
  - [Multi-Tenant Architecture](#multi-tenant-architecture)
  - [Temporal Workflows](#temporal-workflows)
- [Configuration Reference](#configuration-reference)
  - [Environment Variables](#environment-variables)
  - [Worker Configuration](#worker-configuration)
  - [Migration Configuration](#migration-configuration)
- [Troubleshooting](#troubleshooting)

## Overview

SyncNow provides two primary ways to perform database migrations:

1. **Web Application** (Recommended) - Modern NextJS dashboard with real-time monitoring
2. **CLI Tool** - Command-line interface for scripting and automation

Choose the approach that best fits your workflow.

## Quick Start

### For Web Application Users
```bash
git clone https://github.com/virajago/syncnow.git
cd syncnow
make dev-setup-complete
make dev-backend &
make dev-frontend
```
Access at http://localhost:3000

### For CLI Users
```bash
git clone https://github.com/virajago/syncnow.git
cd syncnow
./scripts/build.sh
docker-compose up -d  # Start Temporal
./syncnow worker --config configs/examples/worker-config-sample.yaml &
./syncnow migrate --config configs/examples/sample-config.yaml
```

## Web Application Setup

The web application provides a modern interface for managing database migrations with real-time monitoring and multi-tenant support.

### Prerequisites

- [Go](https://golang.org/doc/install) 1.23 or later
- [Docker](https://docs.docker.com/get-docker/) and Docker Compose V2
- [Node.js](https://nodejs.org/) 18+ and npm
- Source and target database access

### Development Environment

#### One-Command Setup (Recommended)

```bash
# Clone and setup complete environment
git clone https://github.com/virajago/syncnow.git
cd syncnow
make dev-setup-complete
```

This command:
- Starts PostgreSQL, Redis, and Temporal services in Docker
- Runs database migrations
- Creates environment configuration files
- Sets up the complete development environment

#### Start Development Servers

```bash
# Terminal 1: Backend API server
make dev-backend

# Terminal 2: Frontend development server  
make dev-frontend
```

#### Access Applications

- **Frontend**: http://localhost:3000 (NextJS app)
- **Backend API**: http://localhost:8080 (Go API server)
- **Temporal UI**: http://localhost:8090 (Workflow management)

#### Services Overview

- **PostgreSQL** (localhost:5432) - Primary database
- **Redis** (localhost:6379) - Caching and sessions
- **Temporal Server** (localhost:7233) - Workflow engine
- **Temporal UI** (localhost:8090) - Workflow dashboard
- **Go API Server** (localhost:8080) - Backend REST API
- **NextJS App** (localhost:3000) - Frontend application

### Production Deployment

For production deployments, see the deployment configurations in `deployments/docker/`.

## CLI Tool Setup

The CLI tool provides direct command-line access to migration functionality, ideal for automation and scripting.

### Building from Source

```bash
# Build the binary
./scripts/build.sh

# Or directly with Go
go build -o syncnow ./cmd/syncnow
```

### Running with Temporal

SyncNow uses [Temporal](https://temporal.io/) as its workflow engine.

#### Start Temporal Server

```bash
# Using Docker Compose (recommended)
docker-compose up -d

# Or using Docker directly
docker run --detach --name temporal-server \
  -p 7233:7233 \
  temporalio/auto-setup:1.20.0
```

#### Start the Worker

```bash
# Create worker configuration
cp configs/examples/worker-config-sample.yaml worker-config-local.yaml

# Start the worker
./syncnow worker --config worker-config-local.yaml
```

#### Submit Migration Jobs

```bash
# Create migration configuration
cp configs/examples/sample-config.yaml my-migration.yaml

# Submit a migration job
./syncnow migrate --config my-migration.yaml
```

### Configuration

See [Configuration Reference](#configuration-reference) for detailed configuration options.

## SyncNow CLI Commands

The `syncnow` CLI provides multiple commands for managing database migrations, workers, and services.

### Global Flags

These flags are available for all commands:

```bash
-c, --config string           Path to configuration file (required)
    --log-level string        Log level (debug, info, warn, error) (default "info")
    --log-format string       Log format (text, json) (default "text")
    --log-output string       Path to log file (defaults to stdout/stderr)
    --temporal-host string    Temporal server host (default "localhost")
    --temporal-port int       Temporal server port (default 7233)
    --temporal-namespace string Temporal namespace (default "default")
```

### Core Commands

#### `syncnow migrate`

Execute database migration workflows using Temporal for reliability and resume capabilities.

```bash
syncnow migrate --config migration-config.yaml [flags]
```

**Flags:**
- `--schema`: Migrate schema only (overrides config)
- `--data`: Migrate data only (overrides config)

**Examples:**
```bash
# Full migration (schema + data)
syncnow migrate --config configs/examples/sample-config.yaml

# Schema only
syncnow migrate --config configs/examples/sample-config.yaml --schema

# Data only  
syncnow migrate --config configs/examples/sample-config.yaml --data
```

#### `syncnow export`

Export database schema and/or data to files for backup or analysis.

```bash
syncnow export --config export-config.yaml [flags]
```

**Flags:**
- `--schema`: Export schema only (overrides config)
- `--data`: Export data only (overrides config)

**Examples:**
```bash
# Export both schema and data
syncnow export --config configs/examples/sample-config.yaml

# Export schema only
syncnow export --config configs/examples/sample-config.yaml --schema

# Export data only
syncnow export --config configs/examples/sample-config.yaml --data
```

#### `syncnow worker`

Start a Temporal worker to process migration workflows and activities.

```bash
syncnow worker --config worker-config.yaml [flags]
```

**Flags:**
- `-q, --task-queue string`: Temporal task queue name (default "syncnow-task-queue")
- `--metrics-listen-address string`: Prometheus metrics endpoint address (default "0.0.0.0:9090")

**Examples:**
```bash
# Start worker with default settings
syncnow worker --config configs/examples/worker-config-local.yaml

# Start worker with custom task queue
syncnow worker --config worker-config.yaml --task-queue my-queue

# Start worker with custom metrics port
syncnow worker --config worker-config.yaml --metrics-listen-address 0.0.0.0:9091
```

#### `syncnow start`

Start multiple SyncNow services with flexible configuration options.

```bash
syncnow start [--worker] [--server] --config config.yaml [flags]
```

**Flags:**
- `--worker`: Start the Temporal worker service
- `--server`: Start the HTTP API server
- `-q, --task-queue string`: Temporal task queue for worker (default "syncnow-task-queue")
- `--metrics-listen-address string`: Prometheus metrics endpoint (default "0.0.0.0:9090")

**Examples:**
```bash
# Start both API server and worker
syncnow start --worker --server --config server-config.yaml

# Start worker only
syncnow start --worker --config worker-config.yaml

# Start API server only
syncnow start --server --config server-config.yaml
```

#### `syncnow server`

Start combined API server and Temporal worker in a single process.

```bash
syncnow server --config server-config.yaml
```

**Examples:**
```bash
# Start combined server (API + Worker)
syncnow server --config configs/examples/server-config-local.yaml
```

### Database Management Commands

#### `syncnow db`

Database schema management and operations for the SyncNow platform.

```bash
syncnow db [command] [flags]
```

**Database Connection Flags:**
```bash
--db-host string       Database host (default "localhost")
--db-port string       Database port (default "5432")
--db-name string       Database name (default "syncnow")
--db-user string       Database user (default "postgres")
--db-password string   Database password
--db-sslmode string    SSL mode (default "disable")
--database-url string  Database URL (overrides individual flags)
```

#### `syncnow db migrate`

Database migration management with up/down/status/reset operations.

```bash
syncnow db migrate [up|down|status|reset] [flags]
```

**Subcommands:**

##### `syncnow db migrate up`
Apply all pending database migrations.

```bash
syncnow db migrate up --db-host localhost --db-name syncnow_dev
```

##### `syncnow db migrate down`
Rollback the most recent migration.

```bash
syncnow db migrate down --database-url postgres://user:pass@localhost/syncnow
```

##### `syncnow db migrate status`
Show current migration status.

```bash
syncnow db migrate status --db-host localhost --db-name syncnow_dev
```

##### `syncnow db migrate reset`
⚠️ **DANGEROUS**: Drop all tables and re-run all migrations.

```bash
syncnow db migrate reset --database-url postgres://user:pass@localhost/syncnow
# Requires typing 'yes' to confirm
```

#### `syncnow db test-connection`

Test database connectivity with the provided connection parameters.

```bash
syncnow db test-connection --db-host localhost --db-name syncnow_test
```

### Resource Creation Commands

#### `syncnow create`

Create database connections and sync configurations.

```bash
syncnow create [resource-type] --config config.yaml [flags]
```

**Common Flags:**
- `--output string`: Output format (json, yaml, table) (default "table")
- `--tenant string`: Tenant ID (defaults to current user's tenant)
- `--dry-run`: Show what would be created without creating
- `--interactive`: Interactive creation mode
- `--from-file string`: Create from configuration file

**Examples:**
```bash
# Create connection interactively
syncnow create connection --config server-config.yaml --interactive

# Create sync from file
syncnow create sync --config server-config.yaml --from-file sync-config.yaml

# Dry run to preview changes
syncnow create connection --config server-config.yaml --dry-run
```

### Common Usage Patterns

#### 1. Local Development Workflow

```bash
# 1. Start infrastructure
docker-compose up -d

# 2. Run database migrations
syncnow db migrate up --database-url postgres://postgres:postgres@localhost/syncnow_dev

# 3. Start worker in background
syncnow worker --config configs/examples/worker-config-local.yaml &

# 4. Run a migration
syncnow migrate --config configs/examples/sample-config.yaml
```

#### 2. Production Deployment

```bash
# 1. Start combined server (API + Worker)
syncnow start --worker --server --config production-config.yaml

# Or start services separately
syncnow start --server --config api-config.yaml &
syncnow start --worker --config worker-config.yaml &
```

#### 3. Migration Monitoring

```bash
# Check worker metrics
curl http://localhost:9090/metrics

# Monitor via Temporal UI
open http://localhost:8090
```

#### 4. Database Operations

```bash
# Test connection
syncnow db test-connection --database-url $DATABASE_URL

# Check migration status
syncnow db migrate status --database-url $DATABASE_URL

# Apply migrations
syncnow db migrate up --database-url $DATABASE_URL
```

### Environment Variables

Commands support environment variables for database connections:

```bash
export DB_HOST=localhost
export DB_PORT=5432
export DB_NAME=syncnow_dev
export DB_USER=postgres
export DB_PASSWORD=postgres
export DATABASE_URL=postgres://postgres:postgres@localhost/syncnow_dev
```

### Exit Codes

- `0`: Success
- `1`: General error (config, connection, validation)
- `2`: Migration/workflow failure
- `3`: Service startup failure

### Getting Help

```bash
# Show all commands
syncnow --help

# Show command-specific help
syncnow migrate --help
syncnow db migrate --help
syncnow worker --help
```

## Testing

### Quick Test Run

```bash
# Run all tests without database setup
SKIP_DB_TESTS=true ./run_tests.sh

# Run with full database testing
./run_tests.sh

# Run specific test types
./run_tests.sh unit      # Unit tests only
./run_tests.sh schema    # Schema validation only
./run_tests.sh build     # Build verification only
```

### Database Schema Testing

The project includes comprehensive testing for the multi-tenant PostgreSQL database schema, including NextAuth.js compatibility.

#### Test Suite Features

- ✅ **User Model Restructuring**: Global email uniqueness, tenant relationships
- ✅ **NextAuth.js Compatibility**: accounts, sessions, verification_tokens tables
- ✅ **Multi-Tenant Architecture**: User-tenant junction table validation
- ✅ **Migration System**: Up/down migration functionality

#### Focused Database Testing

```bash
# Run only database-related tests
go test -v ./internal/database/...

# Test with coverage
make test-coverage
```

### Integration Testing for Connection Lifecycle

The project includes comprehensive integration tests that test the full connection lifecycle with actual databases.

#### What the Integration Tests Cover

**Connection Lifecycle Operations:**
- Create connection
- Test connection (actual database connectivity)
- Update connection
- Archive connection
- Delete connection
- Parameter validation
- Multi-tenant isolation

**Supported Database Types:**
- PostgreSQL
- MySQL
- SQL Server
- Oracle
- Google Cloud Spanner

#### Running Integration Tests

**Quick Start:**
```bash
# Get environment setup help
./integration_test_env_template.sh help

# Generate minimal config (PostgreSQL only)
./integration_test_env_template.sh minimal

# Generate complete config for all database types
./integration_test_env_template.sh all > .env.integration

# Run all configured database tests
./run_integration_tests.sh --all

# Run specific databases
./run_integration_tests.sh --databases postgresql,mysql

# Run with verbose output and generate report
./run_integration_tests.sh --all --verbose --report
```

#### Integration Test Environment Setup

**Required Environment Variables (per database type):**

**PostgreSQL:**
```bash
export TEST_POSTGRES_HOST=localhost
export TEST_POSTGRES_PORT=5432
export TEST_POSTGRES_DB=syncnow_integration_test
export TEST_POSTGRES_USER=postgres
export TEST_POSTGRES_PASSWORD=postgres
export TEST_POSTGRES_SSLMODE=disable
```

**SQL Server:**
```bash
export TEST_SQLSERVER_HOST=localhost
export TEST_SQLSERVER_PORT=1433
export TEST_SQLSERVER_DB=master
export TEST_SQLSERVER_USER=sa
export TEST_SQLSERVER_PASSWORD=YourStrong@Passw0rd
export TEST_SQLSERVER_ENCRYPT=false
export TEST_SQLSERVER_TRUST_CERT=true
```

**MySQL, Oracle, Spanner** - Similar patterns (see `./integration_test_env_template.sh help`)

**Complete Environment Setup:**
```bash
# Generate configuration for all database types
./integration_test_env_template.sh all > .env.integration

# Edit with your actual credentials
nano .env.integration

# Load environment variables
source .env.integration

# Verify configuration
./integration_test_env_template.sh test
```

#### Docker Setup for Local Testing

For easy local testing, use the provided Docker Compose setup:

```bash
# Check requirements and setup help
./integration_test_env_template.sh requirements

# Start test databases using Docker
./integration_test_env_template.sh docker

# Or manually with docker-compose (save this as docker-compose.test.yml):
docker-compose -f docker-compose.test.yml up -d postgres mysql sqlserver
```

#### Integration Test Features

- **Automatic Skipping**: Tests skip if databases not configured
- **Parallel Execution**: Run multiple database tests simultaneously
- **Detailed Reporting**: Comprehensive error reporting and coverage
- **Real Database Testing**: Uses actual database connectors
- **Multi-tenant Testing**: Tests tenant isolation and permissions
- **Environment Validation**: Pre-flight checks for connectivity

#### Integration Test Commands

```bash
# Test all configured databases
./run_integration_tests.sh --all

# Test specific database types
./run_integration_tests.sh --databases postgresql,sqlserver

# Dry run (show what would be tested)
./run_integration_tests.sh --all --dry-run

# Run with verbose output
./run_integration_tests.sh --databases postgresql --verbose

# Generate detailed test report
./run_integration_tests.sh --all --report

# Run tests in parallel
./run_integration_tests.sh --all --parallel

# Skip all integration tests
SKIP_INTEGRATION_TESTS=true ./run_integration_tests.sh --all
```

### Test Environment Setup

#### Option 1: Docker PostgreSQL (Recommended)
```bash
# Start PostgreSQL container for testing
make docker-db-test
```

#### Option 2: Local PostgreSQL
```bash
# Install PostgreSQL
sudo apt-get install postgresql postgresql-contrib

# Create test database
sudo -u postgres createdb syncnow_test
sudo -u postgres psql -c "ALTER USER postgres PASSWORD 'postgres';"

# Run tests
./run_tests.sh db
```

#### Option 3: Skip Database Tests
```bash
# For CI/CD or environments without PostgreSQL
SKIP_DB_TESTS=true ./run_tests.sh
```

## Development Guide

### Project Structure

```
syncnow/
├── cmd/                    # CLI applications
│   ├── syncnow/           # Main CLI tool
│   └── api-server/        # API server
├── api/                   # Public REST API
│   ├── handlers/          # HTTP request handlers
│   ├── middleware/        # API middleware
│   └── routes/           # Route definitions
├── internal/             # Private application code
│   ├── api/              # Internal API server logic
│   ├── config/           # Configuration management
│   ├── connector/        # Database connectors
│   ├── database/         # Database layer (Postgres)
│   ├── engine/           # Migration engine
│   ├── temporal/         # Temporal workflows
│   └── utils/            # Utilities and metrics
├── pkg/                  # Public packages
│   ├── adapter/          # Type mappings
│   └── validator/        # Validation logic
├── web/                  # NextJS Web interface
│   ├── src/              # Source code
│   ├── components/       # React components
│   └── dashboard/        # Dashboard pages
└── docs/                 # Documentation
```

### Development Commands

```bash
# Environment management
make dev-services          # Start all Docker services
make dev-stop              # Stop all services
make dev-clean             # Clean up (removes volumes)
make dev-logs              # View service logs

# Application servers
make dev-backend           # Start Go API server
make dev-frontend          # Start NextJS dev server

# Database operations
make db-migrate-up         # Run migrations
make db-migrate-down       # Rollback migrations
make db-migrate-status     # Check migration status

# Testing and quality
make test                  # Run all tests
make lint                  # Run code linting

# Cross-Platform Build System
make build                 # Build for multiple platforms using Zig cross-compilation
make build-native          # Build for current platform only
make build-xgo             # Build using xgo and Docker
make build-local           # Build locally (legacy)

# Web Application Build & Packaging
make build-web             # Build Next.js web application
make package-web           # Package web app for distribution

# Distribution & Packaging
make dist                  # Create complete distribution (current platform)
make dist-all              # Create distributions for all platforms
make dist-clean            # Clean all build artifacts
```

### Cross-Platform Build System

SyncNow now includes a comprehensive cross-platform build system using multiple compilation methods.

#### Build Methods

The Makefile provides several build methods to support different environments and requirements:

**`make build` (Default - Zig Cross-Compilation):**
- Uses Zig compiler for cross-compilation
- Builds for Linux/AMD64 and Windows/AMD64 by default
- Requires Zig to be installed ([download here](https://ziglang.org/download/))
- Best for most cross-platform builds

**`make build-native`:**
- Builds only for the current platform
- Fastest option for local development
- No additional dependencies required

**`make build-xgo`:**
- Uses xgo and Docker for cross-compilation
- Requires Docker to be running
- Alternative for complex cross-compilation scenarios

**`make build-local`:**
- Legacy local build using `go build` directly
- For quick local testing only

#### Cross-Platform Build Examples

```bash
# Build for multiple platforms (default: Linux + Windows)
make build

# Build for current platform only
make build-native

# Build using Docker/xgo
make build-xgo

# Custom platforms using build script directly
./scripts/build.sh --zig --platforms "linux/amd64 darwin/amd64 windows/amd64"
```

#### Build Output Structure

All builds create organized output in the `dist/` directory:

```
dist/
├── linux/
│   └── amd64/
│       ├── syncnow
│       ├── syncnow.sha256
│       └── syncnow-dev-linux-amd64.tar.gz
├── windows/
│   └── amd64/
│       ├── syncnow.exe
│       ├── syncnow.exe.sha256
│       └── syncnow-dev-windows-amd64.zip
└── webapp/
    └── [web application files]
```

### Distribution & Packaging

#### Single Platform Distribution

**`make dist`:**
- Builds API binary for current platform
- Packages web application
- Creates complete distribution package with:
  - `bin/syncnow` - API server binary
  - `webapp/` - Web application (standalone)
  - `config/` - Configuration files
  - `README.md` - Quick start guide

#### Multi-Platform Distribution

**`make dist-all`:**
- Creates distribution packages for ALL built platforms
- Combines each platform's binary with web app
- Generates separate archives for each platform
- Perfect for release preparation

#### Web-Only Packaging

**`make build-web`:**
- Installs npm dependencies
- Builds Next.js standalone app

**`make package-web`:**
- Builds web app
- Copies standalone files to `dist/webapp/`
- Copies static assets and public files
- Creates `.tar.gz` and `.zip` archives

#### Usage Examples

```bash
# Quick local distribution
make dist

# Multi-platform release build
make build && make dist-all

# Web app only
make package-web

# Clean everything
make dist-clean
```

#### Distribution Package Structure

**Single Platform (`make dist`):**
```
syncnow-v1.0.0/
├── bin/
│   └── syncnow          # API server binary
├── webapp/              # Web application (standalone)
│   ├── server.js        # Next.js server
│   ├── .next/           # Built application
│   └── public/          # Static assets
├── config/
│   └── server-config.yaml  # Configuration template
└── README.md            # Quick start guide
```

**Multi-Platform (`make dist-all`):**
```
dist/releases/
├── syncnow-v1.0.0-linux-amd64.tar.gz
├── syncnow-v1.0.0-windows-amd64.tar.gz
├── syncnow-v1.0.0-darwin-amd64.tar.gz
└── [platform-specific directories]
```

#### Prerequisites for Cross-Platform Building

**For Zig Cross-Compilation (Recommended):**
```bash
# Install Zig from https://ziglang.org/download/
# Or using package managers:

# macOS
brew install zig

# Ubuntu/Debian
sudo snap install zig --classic

# Arch Linux
sudo pacman -S zig

# Verify installation
zig version
```

**For XGO Cross-Compilation:**
```bash
# Requires Docker
docker --version

# XGO will be auto-installed when needed
make build-xgo
```

#### Deployment

**Single Binary Deployment:**
```bash
# Extract distribution
tar -xzf syncnow-v1.0.0.tar.gz
cd syncnow-v1.0.0

# Configure
cp config/server-config.yaml config/my-config.yaml
# Edit config/my-config.yaml with your settings

# Start API server
./bin/syncnow start server --config config/my-config.yaml &

# Start web application
cd webapp && node server.js &

# Access application
open http://localhost:3000
```

**Platform-Specific Deployment:**
```bash
# Linux
tar -xzf syncnow-v1.0.0-linux-amd64.tar.gz

# Windows
unzip syncnow-v1.0.0-windows-amd64.zip

# macOS
tar -xzf syncnow-v1.0.0-darwin-amd64.tar.gz
```

### Contributing

To extend the system:

1. **Add new metrics**: Update `internal/utils/metrics.go`
2. **Add new API endpoints**: Create handlers in `api/handlers/`
3. **Add database models**: Update `internal/database/models/`
4. **Extend the dashboard**: Add components in `web/src/components/`
5. **Add new tests**: Follow patterns in `internal/database/*_test.go`

## Architecture & Features

### Unified Metrics System

The metrics system centralizes all migration progress, performance metrics, and checkpoints.

#### Key Features
- **Centralized Collection**: All metrics stored in central registry
- **Real-time Progress**: Schema conversion and data migration tracking
- **Performance Measurement**: Rows/second, phase durations, resource utilization
- **Checkpoint Management**: Unified checkpoint system for resume capabilities
- **Error Tracking**: Categorized error recording for troubleshooting
- **Extensible Design**: Easy to add new metric types and visualizations

### Multi-Tenant Architecture

- **User-Tenant Junction**: Many-to-many relationships with roles
- **Tenant Isolation**: Data filtering by tenant_id
- **Cross-Tenant Access**: Super admin access patterns
- **Role-Based Permissions**: tenant_admin, migration_manager, viewer

### Temporal Workflows

SyncNow leverages Temporal for reliable, resumable migrations with workflow orchestration.

## Configuration Reference

### Environment Variables

Create `.env` file or set environment variables:

```bash
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=syncnow_dev
DB_USER=postgres
DB_PASSWORD=postgres
DB_SSLMODE=disable

# API Configuration
API_PORT=8080
API_HOST=localhost

# Temporal Configuration
TEMPORAL_HOST=localhost
TEMPORAL_PORT=7233

# Frontend Configuration (web/.env.local)
NEXT_PUBLIC_API_URL=http://localhost:8080
NEXTAUTH_SECRET=your-secret-key
NEXTAUTH_URL=http://localhost:3000
```

### Worker Configuration

Worker configuration defines how the worker connects to Temporal and executes tasks. See `configs/examples/worker-config-sample.yaml` for a complete example.

### Migration Configuration

Migration configuration defines source and target databases along with migration parameters. See `configs/examples/migration-source-target-sample.yaml` for details.

## Troubleshooting

### Development Environment Issues

#### Docker Compose Issues
```bash
# Check Docker Compose version
docker compose version

# If using old docker-compose, create symlink
sudo ln -s /usr/local/bin/docker-compose /usr/bin/docker-compose
```

#### Services Not Starting
```bash
# Check Docker daemon
sudo systemctl status docker

# View service logs
make dev-logs

# Clean up and restart
make dev-clean && make dev-services
```

#### Port Conflicts
```bash
# Check port usage
sudo lsof -i :5432  # PostgreSQL
sudo lsof -i :3000  # Frontend
sudo lsof -i :8080  # Backend API

# Stop conflicting services or change ports in docker-compose.dev.yml
```

### Database Issues

#### Connection Failures
```bash
# Check PostgreSQL container
docker ps | grep postgres

# Test connection
docker exec -it syncnow-dev-postgres psql -U postgres -d syncnow_dev

# Check migration status
make db-migrate-status
```

#### Migration Errors
```bash
# Reset and retry (CAUTION: destroys data)
make db-migrate-reset
make db-migrate-up

# Run migrations manually
./syncnow db migrate up
```

### Frontend Issues

#### NextJS Build Errors
```bash
# Clear cache and reinstall
cd web && rm -rf .next node_modules
npm install

# Check Node.js version (should be 18+)
node --version
```

#### API Connection Issues
```bash
# Verify backend is running
curl http://localhost:8080/api/health

# Check environment variables
cat web/.env.local | grep NEXT_PUBLIC_API_URL
```

### Temporal Issues

#### Connection Problems
```bash
# Verify Temporal server
curl http://localhost:7233/health

# Check task queue configuration
grep task_queue configs/examples/worker-config-sample.yaml
```

#### Worker Not Processing
1. Verify task queue names match between client and worker
2. Check worker logs for errors
3. Restart Temporal server and worker

### Migration Failures

1. Check worker logs for detailed error messages
2. Verify database credentials and connectivity
3. For data type issues, check schema mapping
4. Enable checkpointing for resumable migrations

### Getting Help

```bash
# Check all service logs
make dev-logs

# View individual service logs
docker compose -f docker-compose.dev.yml logs postgres
docker compose -f docker-compose.dev.yml logs temporal

# Clean slate restart
make dev-clean
make dev-setup-complete

# Check system resources
docker system df
```

For additional support, check the documentation in the `docs/` directory or create an issue on the project repository.
