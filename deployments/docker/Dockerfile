# --- Build Stage ---
FROM golang:1.23-alpine AS builder

# Install build dependencies for CGO (needed for Oracle driver)
RUN apk add --no-cache gcc musl-dev

WORKDIR /app

# Restore dependencies
COPY go.mod go.sum ./
RUN go mod download

# Copy source code
COPY . .

# Build the single binary for syncnow within the cmd/syncnow directory
# Pass version information if needed via ldflags (optional)
# CGO_ENABLED=1 is required for Oracle driver (godror)
RUN CGO_ENABLED=1 GOOS=linux GOARCH=amd64 go build -ldflags="-s -w" -o /syncnow ./cmd/syncnow

# --- Final Stage ---
FROM alpine:latest

# Optional: Install ca-certificates for TLS connections
RUN apk update && apk add --no-cache ca-certificates

WORKDIR /app

# Copy the built binary from the builder stage
COPY --from=builder /syncnow /app/syncnow

# Copy configuration files that might be needed (if not using ConfigMap exclusively)
# COPY config.production.yaml /app/config.yaml

# Set the entrypoint
ENTRYPOINT ["/app/syncnow"]

# Default command (can be overridden) - useful for testing container
# CMD ["--help"]