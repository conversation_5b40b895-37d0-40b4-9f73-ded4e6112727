apiVersion: v2
name: syncnow
description: A Helm chart for SyncNow - Database Migration and Synchronization Platform with Temporal Workflows
type: application
version: 0.2.0
appVersion: "1.0.0"
keywords:
  - database
  - migration
  - synchronization
  - temporal
  - grpc
  - grpc-web
  - envoy
  - nextjs
  - postgres
  - redis
  - kubernetes
  - gke
home: https://github.com/virajago/syncnow
sources:
  - https://github.com/virajago/syncnow
maintainers:
  - name: <PERSON> Rajagopal
    email: <EMAIL>
annotations:
  category: Database
  licenses: MIT
dependencies:
  - name: postgresql
    version: "12.12.10"
    repository: https://charts.bitnami.com/bitnami
    condition: postgresql.enabled
    tags:
      - database

