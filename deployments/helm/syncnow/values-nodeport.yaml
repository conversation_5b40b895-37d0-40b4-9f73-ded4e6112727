# NodePort-based deployment values for SyncNow Helm Chart
# This file is for internal deployments using NodePort services

# Global configuration
global:
  imageRegistry: ""
  imagePullSecrets: []
  storageClass: ""

# Image configuration
image:
  registry: gcr.io
  repository: your-gke-project/syncnow
  tag: "latest"
  pullPolicy: IfNotPresent

# Server configuration with NodePort service
server:
  enabled: true
  replicaCount: 1
  
  image:
    repository: syncnow-server
    tag: "latest"
  
  service:
    type: NodePort
    port: 8090
    targetPort: 8090
    nodePort: 30090  # Fixed NodePort for consistent access
    annotations: {}
  
  # Disable ingress since we're using NodePort
  ingress:
    enabled: false
  
  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 500m
      memory: 512Mi
  
  autoscaling:
    enabled: false  # Disable for simplicity
  
  env:
    LOG_LEVEL: "info"
    SERVER_PORT: "8090"

# Worker configuration
worker:
  enabled: true
  replicaCount: 1
  
  image:
    repository: syncnow-worker
    tag: "latest"
  
  resources:
    limits:
      cpu: 2000m
      memory: 2Gi
    requests:
      cpu: 500m
      memory: 512Mi
  
  autoscaling:
    enabled: false  # Disable for simplicity
  
  env:
    LOG_LEVEL: "info"

# Web frontend configuration with NodePort
web:
  enabled: true
  replicaCount: 1
  
  image:
    repository: syncnow-web
    tag: "latest"
  
  service:
    type: NodePort
    port: 3000
    targetPort: 3000
    nodePort: 30300  # Fixed NodePort for consistent access
    annotations: {}
  
  # Disable ingress since we're using NodePort
  ingress:
    enabled: false
  
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 250m
      memory: 256Mi
  
  autoscaling:
    enabled: false  # Disable for simplicity
  
  env:
    NODE_ENV: "production"
    # These will be set to the Node IPs + NodePorts
    NEXT_PUBLIC_API_URL: "http://NODE_IP:30080"
    NEXT_PUBLIC_GRPC_WEB_URL: "http://NODE_IP:30080"

# Envoy proxy configuration with NodePort
envoy:
  enabled: true
  replicaCount: 1
  
  image:
    repository: envoyproxy/envoy
    tag: "v1.28-latest"
  
  service:
    type: NodePort
    port: 8080
    targetPort: 8080
    nodePort: 30080  # Fixed NodePort for consistent access
    annotations: {}
  
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 250m
      memory: 256Mi

# Disable Temporal for simplicity (or use external)
temporal:
  enabled: false
  external:
    enabled: false
  internal:
    enabled: false

# Use built-in PostgreSQL
postgresql:
  enabled: true
  auth:
    postgresPassword: "postgres"
    username: "syncnow"
    password: "syncnow"
    database: "syncnow"
  primary:
    persistence:
      enabled: true
      size: 5Gi
    resources:
      limits:
        cpu: 500m
        memory: 512Mi
      requests:
        cpu: 250m
        memory: 256Mi



# Disable external database
externalDatabase:
  enabled: false

# Basic secrets
secrets:
  jwtSecret: "dev-jwt-secret"
  encryptionKey: "dev-encryption-key"
  additional: {}

# Disable monitoring
monitoring:
  enabled: false

# Disable network policies
networkPolicy:
  enabled: false

# Disable pod disruption budget for simplicity
podDisruptionBudget:
  enabled: false

# Development configurations
production:
  enabled: false

# Relaxed security context for development
podSecurityContext:
  fsGroup: 2000

securityContext:
  capabilities:
    drop:
    - ALL
  readOnlyRootFilesystem: true
  runAsNonRoot: true
  runAsUser: 1000
