# Kind-specific values for SyncNow deployment
global:
  imageRegistry: ""
  imagePullSecrets: []

image:
  registry: ""
  repository: syncnow
  tag: "local"
  pullPolicy: Never

server:
  enabled: true
  replicaCount: 1
  image:
    repository: syncnow
    tag: "local"
    pullPolicy: Never
  service:
    type: NodePort
    port: 8090
    targetPort: 8090
    nodePort: 30090
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 250m
      memory: 256Mi
  auth:
    enabled: false
  # Increase timeouts for debugging
  livenessProbe:
    httpGet:
      path: /health
      port: 8090
    initialDelaySeconds: 60
    periodSeconds: 30
    timeoutSeconds: 10
    failureThreshold: 5
  readinessProbe:
    httpGet:
      path: /health
      port: 8090
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 10
    failureThreshold: 5

worker:
  enabled: true
  replicaCount: 1
  image:
    repository: syncnow
    tag: "local"
    pullPolicy: Never
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 250m
      memory: 256Mi

web:
  enabled: true
  replicaCount: 1
  image:
    repository: syncnow
    tag: "local"
    pullPolicy: Never
  service:
    type: NodePort
    port: 3000
    targetPort: 3000
    nodePort: 30300
  resources:
    limits:
      cpu: 250m
      memory: 256Mi
    requests:
      cpu: 100m
      memory: 128Mi
  env:
    NODE_ENV: "development"
    NEXT_PUBLIC_API_URL: "http://localhost:30080"
    NEXT_PUBLIC_GRPC_WEB_URL: "http://localhost:30080"

envoy:
  enabled: true
  replicaCount: 1
  service:
    type: NodePort
    port: 8080
    targetPort: 8080
    nodePort: 30080
  resources:
    limits:
      cpu: 250m
      memory: 256Mi
    requests:
      cpu: 100m
      memory: 128Mi

postgresql:
  enabled: true
  auth:
    postgresPassword: "postgres"
    username: "syncnow"
    password: "syncnow"
    database: "syncnow"
  primary:
    persistence:
      enabled: false
    resources:
      limits:
        cpu: 250m
        memory: 256Mi
      requests:
        cpu: 100m
        memory: 128Mi

temporal:
  enabled: true
  internal:
    enabled: true
    replicaCount: 1
    image:
      repository: temporalio/auto-setup
      tag: "1.22.0"
      pullPolicy: IfNotPresent
    service:
      type: ClusterIP
      port: 7233
      targetPort: 7233
      uiPort: 8080
      uiTargetPort: 8080
    resources:
      limits:
        cpu: 500m
        memory: 512Mi
      requests:
        cpu: 250m
        memory: 256Mi
    # Use proper Temporal server configuration
    config:
      logLevel: "info"
      persistence:
        default:
          driver: "sql"
          sql:
            driver: "postgres12"
            host: "syncnow-postgresql"
            port: 5432
            database: "temporal"
            user: "syncnow"
            password: "syncnow"
            maxConns: 20
            maxIdleConns: 20
            maxConnLifetime: "1h"
        visibility:
          driver: "sql"
          sql:
            driver: "postgres12"
            host: "syncnow-postgresql"
            port: 5432
            database: "temporal_visibility"
            user: "syncnow"
            password: "syncnow"
            maxConns: 20
            maxIdleConns: 20
            maxConnLifetime: "1h"
    # Override security context for Temporal
    securityContext:
      capabilities:
        drop:
        - ALL
      readOnlyRootFilesystem: false
      runAsNonRoot: true
      runAsUser: 1000
  external:
    enabled: false

database:
  sslMode: "disable"

dbInit:
  enabled: true
  resources:
    limits:
      cpu: 250m
      memory: 256Mi
    requests:
      cpu: 100m
      memory: 128Mi

externalDatabase:
  enabled: false

monitoring:
  enabled: false

networkPolicy:
  enabled: false

podDisruptionBudget:
  enabled: false
