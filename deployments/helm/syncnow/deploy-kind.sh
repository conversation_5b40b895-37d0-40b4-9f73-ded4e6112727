#!/bin/bash

# SyncNow Kind Deployment Script
# This script automates the deployment of SyncNow to a local Kind cluster

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
CLUSTER_NAME="syncnow-cluster"
NAMESPACE="default"
RELEASE_NAME="syncnow"
BUILD_IMAGES=true
CREATE_CLUSTER=false

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Deploy SyncNow to a local Kind cluster

OPTIONS:
    -c, --create-cluster     Create a new Kind cluster
    -n, --cluster-name       Kind cluster name [default: syncnow-cluster]
    --namespace              Kubernetes namespace [default: default]
    -r, --release            Helm release name [default: syncnow]
    --no-build               Skip building Docker images
    -h, --help               Show this help message

EXAMPLES:
    # Deploy to existing cluster
    $0

    # Create new cluster and deploy
    $0 --create-cluster

    # Deploy with custom cluster name
    $0 --cluster-name my-cluster

    # Skip image building
    $0 --no-build

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--create-cluster)
            CREATE_CLUSTER=true
            shift
            ;;
        -n|--cluster-name)
            CLUSTER_NAME="$2"
            shift 2
            ;;
        --namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -r|--release)
            RELEASE_NAME="$2"
            shift 2
            ;;
        --no-build)
            BUILD_IMAGES=false
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

print_status "Starting SyncNow Kind deployment..."
print_status "Cluster: $CLUSTER_NAME"
print_status "Namespace: $NAMESPACE"
print_status "Release: $RELEASE_NAME"

# Check if required tools are installed
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    local missing_tools=()
    
    if ! command -v kind &> /dev/null; then
        missing_tools+=("kind")
    fi
    
    if ! command -v kubectl &> /dev/null; then
        missing_tools+=("kubectl")
    fi
    
    if ! command -v helm &> /dev/null; then
        missing_tools+=("helm")
    fi
    
    if ! command -v docker &> /dev/null; then
        missing_tools+=("docker")
    fi
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        print_error "Missing required tools: ${missing_tools[*]}"
        print_error "Please install them and try again. See KIND-DEPLOYMENT.md for instructions."
        exit 1
    fi
    
    print_success "All prerequisites are installed"
}

# Create Kind cluster
create_kind_cluster() {
    print_status "Creating Kind cluster: $CLUSTER_NAME"
    
    # Check if cluster already exists
    if kind get clusters | grep -q "^$CLUSTER_NAME$"; then
        print_warning "Cluster '$CLUSTER_NAME' already exists. Skipping creation."
        return
    fi
    
    # Create cluster configuration
    cat > /tmp/kind-config.yaml << EOF
kind: Cluster
apiVersion: kind.x-k8s.io/v1alpha4
name: $CLUSTER_NAME
nodes:
- role: control-plane
  kubeadmConfigPatches:
  - |
    kind: InitConfiguration
    nodeRegistration:
      kubeletExtraArgs:
        node-labels: "ingress-ready=true"
  extraPortMappings:
  - containerPort: 30300
    hostPort: 30300
    protocol: TCP
  - containerPort: 30080
    hostPort: 30080
    protocol: TCP
  - containerPort: 30090
    hostPort: 30090
    protocol: TCP
- role: worker
EOF
    
    kind create cluster --config /tmp/kind-config.yaml
    rm /tmp/kind-config.yaml
    
    print_success "Kind cluster created successfully"
}

# Build and load Docker images
build_and_load_images() {
    if [ "$BUILD_IMAGES" = false ]; then
        print_status "Skipping image building (--no-build specified)"
        return
    fi
    
    print_status "Building Docker images..."
    
    # Navigate to project root (assuming we're in deployments/helm/syncnow)
    PROJECT_ROOT="../../../"

    # Build main SyncNow image using the Dockerfile in deployments/docker
    print_status "Building syncnow:local image..."
    docker build -t syncnow:local -f "$PROJECT_ROOT/deployments/docker/Dockerfile" "$PROJECT_ROOT"

    # Check if web directory exists and build web app image
    if [ -d "$PROJECT_ROOT/web" ] && [ -f "$PROJECT_ROOT/web/Dockerfile" ]; then
        print_status "Building syncnow-web:local image..."
        # Build from project root context to access proto files, but use web/Dockerfile
        if docker build -t syncnow-web:local -f "$PROJECT_ROOT/web/Dockerfile" "$PROJECT_ROOT"; then
            print_status "Web image built successfully"
        else
            print_warning "Web image build failed, using syncnow:local image for web component"
        fi
    else
        print_warning "Web directory or Dockerfile not found, skipping web image build"
        print_status "Using syncnow:local image for web component as well"
    fi
    
    print_status "Loading images into Kind cluster..."
    kind load docker-image syncnow:local --name "$CLUSTER_NAME"

    # Load web image only if it was built
    if docker images | grep -q "syncnow-web.*local"; then
        kind load docker-image syncnow-web:local --name "$CLUSTER_NAME"
    else
        print_status "Using syncnow:local image for web component"
    fi
    
    print_success "Images built and loaded successfully"
}

# Create values file for Kind
create_kind_values() {
    print_status "Creating Kind-specific values file..."

    # Determine which image to use for web component
    if docker images | grep -q "syncnow-web.*local"; then
        WEB_IMAGE_REPO="syncnow-web"
        print_status "Using syncnow-web:local image for web component"
    else
        WEB_IMAGE_REPO="syncnow"
        print_status "Using syncnow:local image for web component"
    fi

    cat > values-kind.yaml << EOF
# Kind-specific values for SyncNow deployment
global:
  imageRegistry: ""
  imagePullSecrets: []

image:
  registry: ""
  repository: syncnow
  tag: "local"
  pullPolicy: Never

server:
  enabled: true
  replicaCount: 1
  image:
    repository: syncnow
    tag: "local"
    pullPolicy: Never
  service:
    type: NodePort
    port: 8090
    targetPort: 8090
    nodePort: 30090
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 250m
      memory: 256Mi
  auth:
    enabled: false

worker:
  enabled: true
  replicaCount: 1
  image:
    repository: syncnow
    tag: "local"
    pullPolicy: Never
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 250m
      memory: 256Mi

web:
  enabled: true
  replicaCount: 1
  image:
    repository: $WEB_IMAGE_REPO
    tag: "local"
    pullPolicy: Never
  service:
    type: NodePort
    port: 3000
    targetPort: 3000
    nodePort: 30300
  resources:
    limits:
      cpu: 250m
      memory: 256Mi
    requests:
      cpu: 100m
      memory: 128Mi
  env:
    NODE_ENV: "development"
    NEXT_PUBLIC_API_URL: "http://localhost:30080"
    NEXT_PUBLIC_GRPC_WEB_URL: "http://localhost:30080"

envoy:
  enabled: true
  replicaCount: 1
  service:
    type: NodePort
    port: 8080
    targetPort: 8080
    nodePort: 30080
  resources:
    limits:
      cpu: 250m
      memory: 256Mi
    requests:
      cpu: 100m
      memory: 128Mi

postgresql:
  enabled: true
  auth:
    postgresPassword: "postgres"
    username: "syncnow"
    password: "syncnow"
    database: "syncnow"
  primary:
    persistence:
      enabled: false
    resources:
      limits:
        cpu: 250m
        memory: 256Mi
      requests:
        cpu: 100m
        memory: 128Mi

temporal:
  enabled: false

externalDatabase:
  enabled: false

monitoring:
  enabled: false

networkPolicy:
  enabled: false

podDisruptionBudget:
  enabled: false
EOF
    
    print_success "Kind values file created"
}

# Deploy with Helm
deploy_helm() {
    print_status "Deploying SyncNow with Helm..."
    
    # Set kubectl context
    kubectl config use-context "kind-$CLUSTER_NAME"
    
    # Create namespace if it doesn't exist
    kubectl create namespace "$NAMESPACE" --dry-run=client -o yaml | kubectl apply -f -
    
    # Update Helm dependencies
    print_status "Updating Helm dependencies..."
    helm dependency update
    
    # Deploy or upgrade
    if helm status "$RELEASE_NAME" -n "$NAMESPACE" &> /dev/null; then
        print_status "Upgrading existing release..."
        helm upgrade "$RELEASE_NAME" . -n "$NAMESPACE" -f values-kind.yaml
    else
        print_status "Installing new release..."
        helm install "$RELEASE_NAME" . -n "$NAMESPACE" -f values-kind.yaml
    fi
    
    print_status "Waiting for deployment to be ready..."
    kubectl wait --for=condition=available --timeout=300s deployment -l app.kubernetes.io/name=syncnow -n "$NAMESPACE"
    
    print_success "SyncNow deployed successfully!"
}

# Show access information
show_access_info() {
    print_status "=== ACCESS INFORMATION ==="
    print_success "🌐 Web Application: http://localhost:30300"
    print_success "🔌 API Endpoint: http://localhost:30080"
    print_success "🖥️  Server Direct: http://localhost:30090"
    
    print_status "=== USEFUL COMMANDS ==="
    print_status "Check pods: kubectl get pods -l app.kubernetes.io/name=syncnow -n $NAMESPACE"
    print_status "Check services: kubectl get svc -l app.kubernetes.io/name=syncnow -n $NAMESPACE"
    print_status "View logs: kubectl logs -l app.kubernetes.io/component=server -n $NAMESPACE"
    print_status "Port forward (alternative): kubectl port-forward svc/syncnow-web 3000:3000 -n $NAMESPACE"
}

# Main execution
main() {
    check_prerequisites
    
    if [ "$CREATE_CLUSTER" = true ]; then
        create_kind_cluster
    fi
    
    build_and_load_images
    create_kind_values
    deploy_helm
    show_access_info
    
    print_success "SyncNow Kind deployment completed successfully!"
}

# Run main function
main
