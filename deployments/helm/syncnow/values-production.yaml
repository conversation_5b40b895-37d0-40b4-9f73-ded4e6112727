# Production values for SyncNow Helm Chart
# This file contains production-specific overrides for GKE deployment

# Global configuration for production
global:
  imageRegistry: "gcr.io"
  imagePullSecrets:
    - name: gcr-json-key
  storageClass: "ssd"

# Production image configuration
image:
  registry: gcr.io
  repository: your-gke-project/syncnow
  tag: "v1.0.0"
  pullPolicy: IfNotPresent

# Production server configuration
server:
  enabled: true
  replicaCount: 3
  
  image:
    repository: syncnow-server
    tag: "v1.0.0"
  
  service:
    type: ClusterIP
    port: 8090
    targetPort: 8090
    annotations:
      cloud.google.com/neg: '{"ingress": true}'
  
  ingress:
    enabled: true
    className: "gce"
    annotations:
      kubernetes.io/ingress.class: "gce"
      kubernetes.io/ingress.global-static-ip-name: "syncnow-api-ip"
      networking.gke.io/managed-certificates: "syncnow-api-ssl-cert"
      kubernetes.io/ingress.allow-http: "false"
    hosts:
      - host: api.syncnow.yourdomain.com
        paths:
          - path: /
            pathType: Prefix
    tls:
      - secretName: syncnow-api-tls
        hosts:
          - api.syncnow.yourdomain.com
  
  resources:
    limits:
      cpu: 2000m
      memory: 2Gi
    requests:
      cpu: 1000m
      memory: 1Gi
  
  autoscaling:
    enabled: true
    minReplicas: 1
    maxReplicas: 5
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80
  
  env:
    LOG_LEVEL: "info"
    SERVER_PORT: "8090"

  # Server-specific configuration
  auth:
    enabled: true
    defaultUser: "<EMAIL>"
    defaultTenant: "default"

  # Temporal configuration for server (production values)
  temporal:
    maxConcurrentActivityExecution: 1000
    maxConcurrentWorkflowTaskExecution: 500
    maxConcurrentActivityTaskPollers: 20
    maxConcurrentWorkflowTaskPollers: 20
    workerActivitiesPerSecond: 1000.0
    taskQueueActivitiesPerSecond: 1000.0

  # Parallelism configuration for server (production values)
  parallelism:
    enablePartitioning: true
    txnBatchSize: 50000
    maxConcurrentWorkers: 16
    noPartitions: 16
    minRowsToSplit: 500000

  # Logging configuration for server
  logging:
    format: "json"

  # Monitoring configuration for server
  monitoring:
    metricsInterval: 30
    dashboard:
      enabled: true

  # Checkpoint configuration for server
  checkpoint:
    enabled: true
    interval: 300
    migrationName: "production-migration"
    autoRecover: true
    retainCount: 10

# Production worker configuration
worker:
  enabled: true
  replicaCount: 5
  
  image:
    repository: syncnow-worker
    tag: "v1.0.0"
  
  resources:
    limits:
      cpu: 4000m
      memory: 4Gi
    requests:
      cpu: 2000m
      memory: 2Gi
  
  autoscaling:
    enabled: true
    minReplicas: 5
    maxReplicas: 50
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80
  
  env:
    LOG_LEVEL: "info"

  # Temporal configuration for worker (production values)
  temporal:
    maxConcurrentActivityExecution: 1000
    maxConcurrentWorkflowTaskExecution: 500
    maxConcurrentActivityTaskPollers: 5
    maxConcurrentWorkflowTaskPollers: 5
    workerActivitiesPerSecond: 500.0
    taskQueueActivitiesPerSecond: 500.0

  # Parallelism configuration for worker (production values)
  parallelism:
    enablePartitioning: true
    txnBatchSize: 10000
    maxConcurrentWorkers: 16
    noPartitions: 16
    minRowsToSplit: 100000

  # Logging configuration for worker
  logging:
    format: "json"

  # Monitoring configuration for worker
  monitoring:
    metricsInterval: 30
    dashboard:
      enabled: false

  # Checkpoint configuration for worker
  checkpoint:
    enabled: true
    interval: 300
    migrationName: "production-migration"
    autoRecover: true
    retainCount: 10

# Production web frontend configuration
web:
  enabled: true
  replicaCount: 1
  
  image:
    repository: syncnow-web
    tag: "v1.0.0"
  
  service:
    type: ClusterIP
    port: 3000
    targetPort: 3000
    annotations:
      cloud.google.com/neg: '{"ingress": true}'
  
  ingress:
    enabled: true
    className: "gce"
    annotations:
      kubernetes.io/ingress.class: "gce"
      kubernetes.io/ingress.global-static-ip-name: "syncnow-web-ip"
      networking.gke.io/managed-certificates: "syncnow-web-ssl-cert"
      kubernetes.io/ingress.allow-http: "false"
    hosts:
      - host: app.syncnow.yourdomain.com
        paths:
          - path: /
            pathType: Prefix
    tls:
      - secretName: syncnow-web-tls
        hosts:
          - app.syncnow.yourdomain.com
  
  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 500m
      memory: 512Mi
  
  autoscaling:
    enabled: true
    minReplicas: 3
    maxReplicas: 10
    targetCPUUtilizationPercentage: 80
  
  env:
    NODE_ENV: "production"
    NEXT_PUBLIC_API_URL: "https://api.syncnow.yourdomain.com"
    NEXT_PUBLIC_GRPC_WEB_URL: "https://api.syncnow.yourdomain.com"

# Production Envoy proxy configuration
envoy:
  enabled: true
  replicaCount: 1
  
  image:
    repository: envoyproxy/envoy
    tag: "v1.28-latest"
  
  service:
    type: ClusterIP
    port: 8080
    targetPort: 8080
    annotations:
      cloud.google.com/neg: '{"ingress": true}'
  
  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 500m
      memory: 512Mi

# Production Temporal configuration (use external managed Temporal)
temporal:
  enabled: true

  # Task queue configuration
  taskQueue: "syncnow-production-task-queue"

  external:
    enabled: true
    host: "temporal.temporal.svc.cluster.local"
    port: 7233
    namespace: "default"
  internal:
    enabled: false

# Production PostgreSQL configuration (use Cloud SQL)
postgresql:
  enabled: false

# External database configuration for Cloud SQL
externalDatabase:
  enabled: true
  host: "your-cloud-sql-proxy-service"
  port: 5432
  database: "syncnow"
  username: "syncnow"
  password: ""  # Set via secret
  existingSecret: "cloudsql-db-credentials"
  existingSecretPasswordKey: "password"



# Production secrets (set these via external secret management)
secrets:
  jwtSecret: ""  # Set via external secret manager
  encryptionKey: ""  # Set via external secret manager
  additional: {}

# Production monitoring and observability
monitoring:
  enabled: true
  prometheus:
    enabled: true
    serviceMonitor:
      enabled: true
      namespace: "monitoring"
      labels:
        release: prometheus
  grafana:
    enabled: true
  jaeger:
    enabled: true

# Network policies for production security
networkPolicy:
  enabled: true
  ingress:
    - from:
        - namespaceSelector:
            matchLabels:
              name: ingress-nginx
    - from:
        - namespaceSelector:
            matchLabels:
              name: monitoring
  egress:
    - to: []
      ports:
        - protocol: TCP
          port: 443
        - protocol: TCP
          port: 5432
        - protocol: TCP
          port: 6379

# Pod disruption budget for high availability
podDisruptionBudget:
  enabled: true
  minAvailable: 2

# Production-specific configurations
production:
  enabled: true
  useProductionResources: true
  enableSecurityPolicies: true

# Security context for production
podSecurityContext:
  runAsNonRoot: true
  runAsUser: 65534
  fsGroup: 65534
  seccompProfile:
    type: RuntimeDefault

securityContext:
  allowPrivilegeEscalation: false
  capabilities:
    drop:
      - ALL
  readOnlyRootFilesystem: true
  runAsNonRoot: true
  runAsUser: 65534
  seccompProfile:
    type: RuntimeDefault
