{{- if .Values.web.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "syncnow.fullname" . }}-web
  labels:
    {{- include "syncnow.labels" . | nindent 4 }}
    app.kubernetes.io/component: web
  {{- with .Values.web.service.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.web.service.type }}
  ports:
    - port: {{ .Values.web.service.port }}
      targetPort: {{ .Values.web.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "syncnow.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: web
{{- end }}
