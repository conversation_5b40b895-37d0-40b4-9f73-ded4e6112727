{{- if .Values.server.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "syncnow.fullname" . }}-server
  labels:
    {{- include "syncnow.labels" . | nindent 4 }}
    app.kubernetes.io/component: server
  {{- with .Values.server.service.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.server.service.type }}
  ports:
    - port: {{ .Values.server.service.port }}
      targetPort: {{ .Values.server.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "syncnow.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: server
{{- end }}
