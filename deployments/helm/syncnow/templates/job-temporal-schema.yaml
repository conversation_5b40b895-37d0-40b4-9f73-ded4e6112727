{{- if and .Values.temporal.enabled .Values.temporal.internal.enabled }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "syncnow.fullname" . }}-temporal-schema
  labels:
    {{- include "syncnow.labels" . | nindent 4 }}
    app.kubernetes.io/component: temporal-schema
  annotations:
    "helm.sh/hook": post-install,pre-upgrade
    "helm.sh/hook-weight": "-2"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
spec:
  template:
    metadata:
      labels:
        {{- include "syncnow.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: temporal-schema
    spec:
      restartPolicy: Never
      serviceAccountName: default
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: temporal-schema
          securityContext:
            capabilities:
              drop:
              - ALL
            readOnlyRootFilesystem: false
            runAsNonRoot: true
            runAsUser: 1000
          image: "temporalio/admin-tools:1.22.0"
          imagePullPolicy: IfNotPresent
          command: ["sh"]
          args:
            - "-c"
            - |
              echo "Setting up Temporal schema..."
              
              # Setup temporal database schema
              temporal-sql-tool --plugin postgres --ep {{ include "syncnow.databaseHost" . }} -u {{ include "syncnow.databaseUsername" . }} -p {{ include "syncnow.databasePort" . }} --db temporal setup-schema -v 0.0
              temporal-sql-tool --plugin postgres --ep {{ include "syncnow.databaseHost" . }} -u {{ include "syncnow.databaseUsername" . }} -p {{ include "syncnow.databasePort" . }} --db temporal update-schema -d /etc/temporal/schema/postgresql/v96/temporal/versioned
              
              # Setup temporal_visibility database schema
              temporal-sql-tool --plugin postgres --ep {{ include "syncnow.databaseHost" . }} -u {{ include "syncnow.databaseUsername" . }} -p {{ include "syncnow.databasePort" . }} --db temporal_visibility setup-schema -v 0.0
              temporal-sql-tool --plugin postgres --ep {{ include "syncnow.databaseHost" . }} -u {{ include "syncnow.databaseUsername" . }} -p {{ include "syncnow.databasePort" . }} --db temporal_visibility update-schema -d /etc/temporal/schema/postgresql/v96/visibility/versioned
              
              echo "Temporal schema setup completed successfully"
          env:
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "syncnow.databaseSecretName" . }}
                  key: {{ include "syncnow.databaseSecretPasswordKey" . }}
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 250m
              memory: 256Mi
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
