{{- if and .Values.temporal.enabled .Values.temporal.internal.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "syncnow.fullname" . }}-temporal-dynamic-config
  labels:
    {{- include "syncnow.labels" . | nindent 4 }}
    app.kubernetes.io/component: temporal
data:
  development-sql.yaml: |
    # Minimal dynamic config for Temporal
    system.forceSearchAttributesCacheRefreshOnRead:
      - value: true
        constraints: {}
    system.searchAttributesNumberOfKeysLimit:
      - value: 100
        constraints: {}
    system.searchAttributesTotalSizeLimit:
      - value: 40000
        constraints: {}
    system.searchAttributesMapSizeLimit:
      - value: 1000
        constraints: {}
{{- end }}
