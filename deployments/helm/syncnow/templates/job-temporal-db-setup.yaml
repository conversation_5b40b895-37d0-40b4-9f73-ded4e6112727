{{- if and .Values.temporal.enabled .Values.temporal.internal.enabled }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "syncnow.fullname" . }}-temporal-db-setup
  labels:
    {{- include "syncnow.labels" . | nindent 4 }}
    app.kubernetes.io/component: temporal-db-setup
  annotations:
    "helm.sh/hook": post-install,pre-upgrade
    "helm.sh/hook-weight": "-3"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
spec:
  template:
    metadata:
      labels:
        {{- include "syncnow.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: temporal-db-setup
    spec:
      restartPolicy: Never
      serviceAccountName: default
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: temporal-db-setup
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "postgres:13"
          imagePullPolicy: IfNotPresent
          command: ["sh"]
          args:
            - "-c"
            - |
              echo "Creating Temporal databases..."
              export PGPASSWORD="$DB_PASSWORD"
              
              # Create temporal database if it doesn't exist
              psql -h {{ include "syncnow.databaseHost" . }} -p {{ include "syncnow.databasePort" . }} -U {{ include "syncnow.databaseUsername" . }} -d {{ include "syncnow.databaseName" . }} -c "CREATE DATABASE temporal;" || echo "temporal database already exists"
              
              # Create temporal_visibility database if it doesn't exist
              psql -h {{ include "syncnow.databaseHost" . }} -p {{ include "syncnow.databasePort" . }} -U {{ include "syncnow.databaseUsername" . }} -d {{ include "syncnow.databaseName" . }} -c "CREATE DATABASE temporal_visibility;" || echo "temporal_visibility database already exists"
              
              echo "Temporal databases created successfully"
          env:
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "syncnow.databaseSecretName" . }}
                  key: {{ include "syncnow.databaseSecretPasswordKey" . }}
          resources:
            limits:
              cpu: 250m
              memory: 256Mi
            requests:
              cpu: 100m
              memory: 128Mi
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
