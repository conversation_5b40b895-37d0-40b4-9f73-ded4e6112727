{{- if .Values.worker.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "syncnow.fullname" . }}-worker
  labels:
    {{- include "syncnow.labels" . | nindent 4 }}
    app.kubernetes.io/component: worker
spec:
  {{- if not .Values.worker.autoscaling.enabled }}
  replicas: {{ .Values.worker.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "syncnow.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: worker
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
      labels:
        {{- include "syncnow.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: worker
    spec:
      {{- with .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "syncnow.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: worker
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.registry }}/{{ .Values.worker.image.repository }}:{{ .Values.worker.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.worker.image.pullPolicy }}
          command: ["./syncnow"]
          args: ["start", "worker", "--config=/app/config/worker-config.yaml"]
          env:
            - name: DB_HOST
              value: {{ include "syncnow.databaseHost" . | quote }}
            - name: DB_PORT
              value: {{ include "syncnow.databasePort" . | quote }}
            - name: DB_NAME
              value: {{ include "syncnow.databaseName" . | quote }}
            - name: DB_USER
              value: {{ include "syncnow.databaseUsername" . | quote }}
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "syncnow.databaseSecretName" . }}
                  key: {{ include "syncnow.databaseSecretPasswordKey" . }}
            - name: DB_SSLMODE
              value: "require"
            - name: TEMPORAL_HOST
              value: {{ include "syncnow.temporalHost" . | quote }}
            - name: TEMPORAL_PORT
              value: {{ include "syncnow.temporalPort" . | quote }}
            - name: TEMPORAL_NAMESPACE
              value: "default"
            {{- range $key, $value := .Values.worker.env }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
          {{- with .Values.worker.envFrom }}
          envFrom:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          resources:
            {{- toYaml .Values.worker.resources | nindent 12 }}
          volumeMounts:
            - name: config
              mountPath: /app/config
              readOnly: true
            - name: logs
              mountPath: /app/logs
            - name: data
              mountPath: /app/data
      volumes:
        - name: config
          configMap:
            name: {{ include "syncnow.fullname" . }}-config
        - name: logs
          emptyDir: {}
        - name: data
          emptyDir: {}
      {{- with .Values.worker.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.worker.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.worker.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
