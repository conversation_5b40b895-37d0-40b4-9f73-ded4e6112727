{{- if and .Values.temporal.enabled .Values.temporal.internal.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "syncnow.fullname" . }}-temporal-config
  labels:
    {{- include "syncnow.labels" . | nindent 4 }}
    app.kubernetes.io/component: temporal
data:
  development-sql.yaml: |
    # Minimal dynamic config for Temporal
    system.forceSearchAttributesCacheRefreshOnRead:
      - value: true
        constraints: {}
    system.searchAttributesNumberOfKeysLimit:
      - value: 100
        constraints: {}
    system.searchAttributesTotalSizeLimit:
      - value: 40000
        constraints: {}
    system.searchAttributesMapSizeLimit:
      - value: 1000
        constraints: {}
  config.yaml: |
    log:
      stdout: true
      level: {{ .Values.temporal.internal.config.logLevel | default "info" }}
    
    persistence:
      defaultStore: default
      visibilityStore: visibility
      numHistoryShards: 512
      datastores:
        default:
          {{- toYaml .Values.temporal.internal.config.persistence.default | nindent 10 }}
        visibility:
          {{- toYaml .Values.temporal.internal.config.persistence.visibility | nindent 10 }}
    
    global:
      membership:
        maxJoinDuration: 30s
        broadcastAddress: {{ include "syncnow.fullname" . }}-temporal
    
    services:
      frontend:
        rpc:
          grpcPort: 7233
          membershipPort: 6933
          bindOnLocalHost: false
        metrics:
          prometheus:
            timerType: histogram
            listenAddress: "0.0.0.0:9090"
      
      matching:
        rpc:
          grpcPort: 7235
          membershipPort: 6935
          bindOnLocalHost: false
        metrics:
          prometheus:
            timerType: histogram
            listenAddress: "0.0.0.0:9090"
      
      history:
        rpc:
          grpcPort: 7234
          membershipPort: 6934
          bindOnLocalHost: false
        metrics:
          prometheus:
            timerType: histogram
            listenAddress: "0.0.0.0:9090"
      
      worker:
        rpc:
          grpcPort: 7239
          membershipPort: 6939
          bindOnLocalHost: false
        metrics:
          prometheus:
            timerType: histogram
            listenAddress: "0.0.0.0:9090"
    
    clusterMetadata:
      enableGlobalNamespace: false
      failoverVersionIncrement: 10
      masterClusterName: "active"
      currentClusterName: "active"
      clusterInformation:
        active:
          enabled: true
          initialFailoverVersion: 1
          rpcName: "frontend"
          rpcAddress: "{{ include "syncnow.fullname" . }}-temporal:7233"
    
    dcRedirectionPolicy:
      policy: "noop"
    
    archival:
      history:
        state: "disabled"
      visibility:
        state: "disabled"
    
    publicClient:
      hostPort: "{{ include "syncnow.fullname" . }}-temporal:7233"
    
    namespaceDefaults:
      archival:
        history:
          state: "disabled"
        visibility:
          state: "disabled"
{{- end }}
