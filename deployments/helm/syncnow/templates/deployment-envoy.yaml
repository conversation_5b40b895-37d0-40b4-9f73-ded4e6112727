{{- if .Values.envoy.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "syncnow.fullname" . }}-envoy
  labels:
    {{- include "syncnow.labels" . | nindent 4 }}
    app.kubernetes.io/component: envoy
spec:
  replicas: {{ .Values.envoy.replicaCount }}
  selector:
    matchLabels:
      {{- include "syncnow.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: envoy
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
      labels:
        {{- include "syncnow.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: envoy
    spec:
      {{- with .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "syncnow.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: envoy
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.envoy.image.repository }}:{{ .Values.envoy.image.tag }}"
          imagePullPolicy: {{ .Values.envoy.image.pullPolicy }}
          command: ["/usr/local/bin/envoy"]
          args: ["-c", "/etc/envoy/envoy.yaml", "--log-level", "info"]
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
            - name: admin
              containerPort: 9901
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /ready
              port: 9901
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /ready
              port: 9901
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 3
            failureThreshold: 3
          resources:
            {{- toYaml .Values.envoy.resources | nindent 12 }}
          volumeMounts:
            - name: envoy-config
              mountPath: /etc/envoy
              readOnly: true
      volumes:
        - name: envoy-config
          configMap:
            name: {{ include "syncnow.fullname" . }}-config
            items:
              - key: envoy.yaml
                path: envoy.yaml
      {{- with .Values.envoy.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.envoy.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.envoy.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
