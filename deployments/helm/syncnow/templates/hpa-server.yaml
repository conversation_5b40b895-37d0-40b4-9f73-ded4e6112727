{{- if and .Values.server.enabled .Values.server.autoscaling.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "syncnow.fullname" . }}-server
  labels:
    {{- include "syncnow.labels" . | nindent 4 }}
    app.kubernetes.io/component: server
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "syncnow.fullname" . }}-server
  minReplicas: {{ .Values.server.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.server.autoscaling.maxReplicas }}
  metrics:
    {{- if .Values.server.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ .Values.server.autoscaling.targetCPUUtilizationPercentage }}
    {{- end }}
    {{- if .Values.server.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ .Values.server.autoscaling.targetMemoryUtilizationPercentage }}
    {{- end }}
{{- end }}
