{{- if .Values.dbInit.enabled }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "syncnow.fullname" . }}-db-init
  labels:
    {{- include "syncnow.labels" . | nindent 4 }}
    app.kubernetes.io/component: db-init
  annotations:
    "helm.sh/hook": post-install,pre-upgrade
    "helm.sh/hook-weight": "-1"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
spec:
  template:
    metadata:
      labels:
        {{- include "syncnow.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: db-init
    spec:
      restartPolicy: Never
      serviceAccountName: default
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: db-init
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ if .Values.image.registry }}{{ .Values.image.registry }}/{{ end }}{{ .Values.server.image.repository }}:{{ .Values.server.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.server.image.pullPolicy }}
          command: ["./syncnow"]
          args: 
            - "init"
            - "--db-host={{ include "syncnow.databaseHost" . }}"
            - "--db-port={{ include "syncnow.databasePort" . }}"
            - "--db-name={{ include "syncnow.databaseName" . }}"
            - "--db-user={{ include "syncnow.databaseUsername" . }}"
            - "--db-sslmode={{ .Values.database.sslMode | default "require" }}"
          env:
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "syncnow.databaseSecretName" . }}
                  key: {{ include "syncnow.databaseSecretPasswordKey" . }}
          resources:
            {{- toYaml .Values.dbInit.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
