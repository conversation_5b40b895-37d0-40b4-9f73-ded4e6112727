{{- if .Values.podDisruptionBudget.enabled }}
---
{{- if .Values.server.enabled }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ include "syncnow.fullname" . }}-server
  labels:
    {{- include "syncnow.labels" . | nindent 4 }}
    app.kubernetes.io/component: server
spec:
  {{- if .Values.podDisruptionBudget.minAvailable }}
  minAvailable: {{ .Values.podDisruptionBudget.minAvailable }}
  {{- end }}
  {{- if .Values.podDisruptionBudget.maxUnavailable }}
  maxUnavailable: {{ .Values.podDisruptionBudget.maxUnavailable }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "syncnow.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: server
{{- end }}
---
{{- if .Values.worker.enabled }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ include "syncnow.fullname" . }}-worker
  labels:
    {{- include "syncnow.labels" . | nindent 4 }}
    app.kubernetes.io/component: worker
spec:
  {{- if .Values.podDisruptionBudget.minAvailable }}
  minAvailable: {{ .Values.podDisruptionBudget.minAvailable }}
  {{- end }}
  {{- if .Values.podDisruptionBudget.maxUnavailable }}
  maxUnavailable: {{ .Values.podDisruptionBudget.maxUnavailable }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "syncnow.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: worker
{{- end }}
---
{{- if .Values.web.enabled }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ include "syncnow.fullname" . }}-web
  labels:
    {{- include "syncnow.labels" . | nindent 4 }}
    app.kubernetes.io/component: web
spec:
  {{- if .Values.podDisruptionBudget.minAvailable }}
  minAvailable: {{ .Values.podDisruptionBudget.minAvailable }}
  {{- end }}
  {{- if .Values.podDisruptionBudget.maxUnavailable }}
  maxUnavailable: {{ .Values.podDisruptionBudget.maxUnavailable }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "syncnow.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: web
{{- end }}
{{- end }}
