{{- if and .Values.temporal.enabled .Values.temporal.internal.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "syncnow.fullname" . }}-temporal
  labels:
    {{- include "syncnow.labels" . | nindent 4 }}
    app.kubernetes.io/component: temporal
spec:
  replicas: {{ .Values.temporal.internal.replicaCount }}
  selector:
    matchLabels:
      {{- include "syncnow.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: temporal
  template:
    metadata:
      labels:
        {{- include "syncnow.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: temporal
    spec:
      {{- with .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "syncnow.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: temporal
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.temporal.internal.image.repository }}:{{ .Values.temporal.internal.image.tag }}"
          imagePullPolicy: {{ .Values.temporal.internal.image.pullPolicy }}
          ports:
            - name: rpc
              containerPort: 7233
              protocol: TCP
            - name: ui
              containerPort: 8080
              protocol: TCP
          env:
            - name: DB
              value: "postgresql"
            - name: DB_PORT
              value: {{ include "syncnow.databasePort" . | quote }}
            - name: POSTGRES_USER
              value: {{ include "syncnow.databaseUsername" . | quote }}
            - name: POSTGRES_PWD
              valueFrom:
                secretKeyRef:
                  name: {{ include "syncnow.databaseSecretName" . }}
                  key: {{ include "syncnow.databaseSecretPasswordKey" . }}
            - name: POSTGRES_SEEDS
              value: {{ include "syncnow.databaseHost" . | quote }}
            - name: DYNAMIC_CONFIG_FILE_PATH
              value: "config/dynamicconfig/development-sql.yaml"
            {{- range $key, $value := .Values.temporal.internal.env }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
          livenessProbe:
            exec:
              command:
                - tctl
                - --address
                - localhost:7233
                - workflow
                - list
            initialDelaySeconds: 60
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 3
          readinessProbe:
            exec:
              command:
                - tctl
                - --address
                - localhost:7233
                - workflow
                - list
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          resources:
            {{- toYaml .Values.temporal.internal.resources | nindent 12 }}
          {{- if .Values.temporal.internal.persistence.enabled }}
          volumeMounts:
            - name: temporal-data
              mountPath: /etc/temporal
      volumes:
        - name: temporal-data
          persistentVolumeClaim:
            claimName: {{ include "syncnow.fullname" . }}-temporal-data
          {{- end }}
{{- end }}
