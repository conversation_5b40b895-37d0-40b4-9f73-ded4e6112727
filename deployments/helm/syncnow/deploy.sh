#!/bin/bash

# SyncNow Helm Chart Deployment Script
# This script helps deploy SyncNow to GKE with proper configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT="development"
NAMESPACE="default"
RELEASE_NAME="syncnow"
VALUES_FILE=""
PROJECT_ID=""
CLUSTER_NAME=""
CLUSTER_ZONE=""

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Deploy SyncNow to Kubernetes using Helm

OPTIONS:
    -e, --environment    Environment (development|production) [default: development]
    -n, --namespace      Kubernetes namespace [default: default]
    -r, --release        Helm release name [default: syncnow]
    -f, --values-file    Custom values file path
    -p, --project-id     GCP Project ID (required for production)
    -c, --cluster        GKE cluster name (required for production)
    -z, --zone           GKE cluster zone (required for production)
    -h, --help           Show this help message

EXAMPLES:
    # Development deployment
    $0 -e development

    # Production deployment
    $0 -e production -p my-project -c syncnow-cluster -z us-central1-a

    # Custom values file
    $0 -f my-custom-values.yaml

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -r|--release)
            RELEASE_NAME="$2"
            shift 2
            ;;
        -f|--values-file)
            VALUES_FILE="$2"
            shift 2
            ;;
        -p|--project-id)
            PROJECT_ID="$2"
            shift 2
            ;;
        -c|--cluster)
            CLUSTER_NAME="$2"
            shift 2
            ;;
        -z|--zone)
            CLUSTER_ZONE="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate environment
if [[ "$ENVIRONMENT" != "development" && "$ENVIRONMENT" != "production" ]]; then
    print_error "Environment must be either 'development' or 'production'"
    exit 1
fi

# Set values file based on environment if not specified
if [[ -z "$VALUES_FILE" ]]; then
    if [[ "$ENVIRONMENT" == "production" ]]; then
        VALUES_FILE="values-production.yaml"
    else
        VALUES_FILE="values.yaml"
    fi
fi

# Validate production requirements
if [[ "$ENVIRONMENT" == "production" ]]; then
    if [[ -z "$PROJECT_ID" || -z "$CLUSTER_NAME" || -z "$CLUSTER_ZONE" ]]; then
        print_error "Production deployment requires --project-id, --cluster, and --zone"
        exit 1
    fi
fi

print_status "Starting SyncNow deployment..."
print_status "Environment: $ENVIRONMENT"
print_status "Namespace: $NAMESPACE"
print_status "Release: $RELEASE_NAME"
print_status "Values file: $VALUES_FILE"

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is not installed or not in PATH"
    exit 1
fi

# Check if helm is available
if ! command -v helm &> /dev/null; then
    print_error "helm is not installed or not in PATH"
    exit 1
fi

# For production, configure GKE context
if [[ "$ENVIRONMENT" == "production" ]]; then
    print_status "Configuring GKE context..."
    gcloud container clusters get-credentials "$CLUSTER_NAME" --zone="$CLUSTER_ZONE" --project="$PROJECT_ID"
fi

# Create namespace if it doesn't exist
print_status "Creating namespace if it doesn't exist..."
kubectl create namespace "$NAMESPACE" --dry-run=client -o yaml | kubectl apply -f -

# Update Helm dependencies
print_status "Updating Helm dependencies..."
helm dependency update

# Validate Helm chart
print_status "Validating Helm chart..."
helm lint .

# Check if release already exists
if helm status "$RELEASE_NAME" -n "$NAMESPACE" &> /dev/null; then
    print_warning "Release '$RELEASE_NAME' already exists. Upgrading..."
    HELM_COMMAND="upgrade"
else
    print_status "Installing new release '$RELEASE_NAME'..."
    HELM_COMMAND="install"
fi

# Prepare Helm command
HELM_CMD="helm $HELM_COMMAND $RELEASE_NAME . -n $NAMESPACE -f $VALUES_FILE"

# Add production-specific overrides
if [[ "$ENVIRONMENT" == "production" && -n "$PROJECT_ID" ]]; then
    HELM_CMD="$HELM_CMD --set image.registry=gcr.io/$PROJECT_ID"
fi

# Execute Helm command
print_status "Executing: $HELM_CMD"
eval "$HELM_CMD"

# Wait for deployment to be ready
print_status "Waiting for deployment to be ready..."
kubectl wait --for=condition=available --timeout=300s deployment -l app.kubernetes.io/name=syncnow -n "$NAMESPACE"

# Show deployment status
print_status "Deployment status:"
kubectl get pods -l app.kubernetes.io/name=syncnow -n "$NAMESPACE"

print_success "SyncNow deployment completed successfully!"

# Show access information
print_status "Access information:"
if [[ "$ENVIRONMENT" == "production" ]]; then
    print_status "Web App: https://app.syncnow.yourdomain.com"
    print_status "API: https://api.syncnow.yourdomain.com"
else
    print_status "Use 'kubectl port-forward' to access services locally:"
    print_status "Web App: kubectl port-forward svc/$RELEASE_NAME-web 3000:3000 -n $NAMESPACE"
    print_status "API: kubectl port-forward svc/$RELEASE_NAME-envoy 8080:8080 -n $NAMESPACE"
fi

print_status "To check logs:"
print_status "kubectl logs -l app.kubernetes.io/component=server -n $NAMESPACE"
print_status "kubectl logs -l app.kubernetes.io/component=worker -n $NAMESPACE"

print_success "Deployment script completed!"
