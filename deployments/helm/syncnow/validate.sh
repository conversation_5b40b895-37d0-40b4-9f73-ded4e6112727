#!/bin/bash

# SyncNow Helm Chart Validation Script
# This script validates the Helm chart before deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if helm is available
if ! command -v helm &> /dev/null; then
    print_error "helm is not installed or not in PATH"
    exit 1
fi

print_status "Starting SyncNow Helm chart validation..."

# Update dependencies
print_status "Updating Helm dependencies..."
helm dependency update

# Lint the chart
print_status "Linting Helm chart..."
if helm lint .; then
    print_success "Helm lint passed"
else
    print_error "Helm lint failed"
    exit 1
fi

# Template the chart with default values
print_status "Templating chart with default values..."
if helm template syncnow . > /tmp/syncnow-default.yaml; then
    print_success "Default values template generation successful"
else
    print_error "Default values template generation failed"
    exit 1
fi

# Template the chart with production values
if [[ -f "values-production.yaml" ]]; then
    print_status "Templating chart with production values..."
    if helm template syncnow . -f values-production.yaml > /tmp/syncnow-production.yaml; then
        print_success "Production values template generation successful"
    else
        print_error "Production values template generation failed"
        exit 1
    fi
fi

# Validate Kubernetes manifests
print_status "Validating Kubernetes manifests..."
if command -v kubectl &> /dev/null; then
    if kubectl apply --dry-run=client -f /tmp/syncnow-default.yaml > /dev/null 2>&1; then
        print_success "Kubernetes manifest validation passed"
    else
        print_warning "Kubernetes manifest validation failed (this might be due to missing CRDs)"
    fi
else
    print_warning "kubectl not available, skipping Kubernetes manifest validation"
fi

# Check for required templates
print_status "Checking for required templates..."
required_templates=(
    "templates/deployment-server.yaml"
    "templates/deployment-worker.yaml"
    "templates/deployment-web.yaml"
    "templates/deployment-envoy.yaml"
    "templates/service-server.yaml"
    "templates/service-web.yaml"
    "templates/service-envoy.yaml"
    "templates/configmap.yaml"
    "templates/secret.yaml"
    "templates/serviceaccount.yaml"
)

missing_templates=()
for template in "${required_templates[@]}"; do
    if [[ ! -f "$template" ]]; then
        missing_templates+=("$template")
    fi
done

if [[ ${#missing_templates[@]} -eq 0 ]]; then
    print_success "All required templates are present"
else
    print_error "Missing templates:"
    for template in "${missing_templates[@]}"; do
        echo "  - $template"
    done
    exit 1
fi

# Check values files
print_status "Checking values files..."
if [[ ! -f "values.yaml" ]]; then
    print_error "values.yaml not found"
    exit 1
fi

if [[ ! -f "values-production.yaml" ]]; then
    print_warning "values-production.yaml not found"
fi

# Check Chart.yaml
print_status "Checking Chart.yaml..."
if [[ ! -f "Chart.yaml" ]]; then
    print_error "Chart.yaml not found"
    exit 1
fi

# Validate Chart.yaml structure
if ! grep -q "apiVersion: v2" Chart.yaml; then
    print_error "Chart.yaml must use apiVersion v2"
    exit 1
fi

if ! grep -q "type: application" Chart.yaml; then
    print_error "Chart.yaml must specify type: application"
    exit 1
fi

# Check for security best practices
print_status "Checking security best practices..."
security_checks=0

# Check if security contexts are defined
if grep -q "securityContext:" templates/deployment-*.yaml; then
    print_success "Security contexts found in deployments"
    ((security_checks++))
else
    print_warning "No security contexts found in deployments"
fi

# Check if non-root users are used
if grep -q "runAsNonRoot: true" values*.yaml; then
    print_success "Non-root user configuration found"
    ((security_checks++))
else
    print_warning "Non-root user configuration not found"
fi

# Check if read-only root filesystem is used
if grep -q "readOnlyRootFilesystem: true" values*.yaml; then
    print_success "Read-only root filesystem configuration found"
    ((security_checks++))
else
    print_warning "Read-only root filesystem configuration not found"
fi

print_status "Security checks passed: $security_checks/3"

# Check resource limits
print_status "Checking resource limits..."
if grep -q "resources:" templates/deployment-*.yaml && grep -q "limits:" values*.yaml; then
    print_success "Resource limits are configured"
else
    print_warning "Resource limits may not be properly configured"
fi

# Check for health checks
print_status "Checking health checks..."
health_checks=0

if grep -q "livenessProbe:" templates/deployment-*.yaml; then
    print_success "Liveness probes found"
    ((health_checks++))
fi

if grep -q "readinessProbe:" templates/deployment-*.yaml; then
    print_success "Readiness probes found"
    ((health_checks++))
fi

print_status "Health checks found: $health_checks/2"

# Summary
print_status "Validation Summary:"
print_success "✓ Helm lint passed"
print_success "✓ Template generation successful"
print_success "✓ All required templates present"
print_success "✓ Chart.yaml structure valid"

if [[ $security_checks -eq 3 ]]; then
    print_success "✓ All security best practices implemented"
elif [[ $security_checks -gt 0 ]]; then
    print_warning "⚠ Some security best practices implemented ($security_checks/3)"
else
    print_warning "⚠ Security best practices need attention"
fi

if [[ $health_checks -eq 2 ]]; then
    print_success "✓ Health checks configured"
else
    print_warning "⚠ Health checks may need attention"
fi

print_success "SyncNow Helm chart validation completed!"
print_status "Chart is ready for deployment."

# Clean up temporary files
rm -f /tmp/syncnow-*.yaml
