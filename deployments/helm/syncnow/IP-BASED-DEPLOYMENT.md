# SyncNow IP-Based Deployment Guide

This guide explains how to deploy SyncNow for internal users without requiring domain names, using IP addresses instead.

## 🎯 Deployment Options

### Option 1: LoadBalancer Services (Recommended for Cloud)

**Best for:** GKE, EKS, AKS, or any cloud provider with LoadBalancer support
**Pros:** Automatic external IP assignment, production-ready, easy access
**Cons:** Additional cost for LoadBalancer services

```bash
# Deploy using LoadBalancer services
./deploy-ip-based.sh

# Or manually
helm install syncnow . -f values-ip-based.yaml
```

**Access URLs:**
- Web App: `http://EXTERNAL_IP:3000`
- API: `http://EXTERNAL_IP:8080`

### Option 2: NodePort Services

**Best for:** On-premises, development, cost-conscious deployments
**Pros:** No additional costs, works on any Kubernetes cluster
**Cons:** Requires knowledge of node IPs, limited port range (30000-32767)

```bash
# Deploy using NodePort services
helm install syncnow . -f values-nodeport.yaml

# Get node IPs
kubectl get nodes -o wide
```

**Access URLs:**
- Web App: `http://NODE_IP:30300`
- API: `http://NODE_IP:30080`

### Option 3: Port Forwarding (Development)

**Best for:** Local development, testing, temporary access
**Pros:** No service configuration needed, secure
**Cons:** Only accessible from the machine running kubectl

```bash
# Deploy with ClusterIP services
helm install syncnow . -f values.yaml

# Forward ports
kubectl port-forward svc/syncnow-web 3000:3000 &
kubectl port-forward svc/syncnow-envoy 8080:8080 &
```

**Access URLs:**
- Web App: `http://localhost:3000`
- API: `http://localhost:8080`

### Option 4: Ingress with IP-based routing

**Best for:** When you have an ingress controller but no domain
**Pros:** Uses existing ingress infrastructure
**Cons:** Requires ingress controller configuration

## 🚀 Quick Start

### For GKE/Cloud Deployment

```bash
# 1. Clone and navigate to the chart
cd helm/syncnow

# 2. Update values with your project details
cp values-ip-based.yaml my-values.yaml
# Edit my-values.yaml with your image registry

# 3. Deploy
./deploy-ip-based.sh -f my-values.yaml

# 4. Get external IPs
kubectl get svc -l app.kubernetes.io/name=syncnow
```

### For On-Premises/NodePort Deployment

```bash
# 1. Deploy with NodePort
helm install syncnow . -f values-nodeport.yaml

# 2. Get node IPs
kubectl get nodes -o wide

# 3. Access services
# Web: http://NODE_IP:30300
# API: http://NODE_IP:30080
```

## 🔧 Configuration Details

### LoadBalancer Configuration

```yaml
# values-ip-based.yaml
server:
  service:
    type: LoadBalancer
    port: 8090
    annotations:
      # Reserve static IP (GKE)
      cloud.google.com/load-balancer-type: "External"

web:
  service:
    type: LoadBalancer
    port: 3000

envoy:
  service:
    type: LoadBalancer
    port: 8080
```

### NodePort Configuration

```yaml
# values-nodeport.yaml
server:
  service:
    type: NodePort
    port: 8090
    nodePort: 30090  # Fixed port

web:
  service:
    type: NodePort
    port: 3000
    nodePort: 30300  # Fixed port

envoy:
  service:
    type: NodePort
    port: 8080
    nodePort: 30080  # Fixed port
```

## 🔍 Getting Access Information

### After LoadBalancer Deployment

```bash
# Get external IPs
kubectl get svc -l app.kubernetes.io/name=syncnow

# Wait for external IPs to be assigned
kubectl get svc syncnow-web -w
kubectl get svc syncnow-envoy -w
```

### After NodePort Deployment

```bash
# Get node IPs
kubectl get nodes -o wide

# Get NodePort assignments
kubectl get svc -l app.kubernetes.io/name=syncnow

# Access format: http://NODE_IP:NODEPORT
```

## 🛠️ Troubleshooting

### LoadBalancer Issues

**Problem:** External IP shows `<pending>`
**Solutions:**
- Check if your cluster supports LoadBalancer services
- Verify cloud provider credentials
- Check quotas and limits

**Problem:** Cannot access via external IP
**Solutions:**
- Check firewall rules
- Verify security groups (AWS) or firewall rules (GCP)
- Ensure services are running: `kubectl get pods`

### NodePort Issues

**Problem:** Cannot access via NodePort
**Solutions:**
- Check if nodes are accessible from your network
- Verify firewall rules on nodes
- Ensure NodePort range is allowed (30000-32767)

**Problem:** NodePort conflicts
**Solutions:**
- Use different NodePort values
- Let Kubernetes assign automatically (remove nodePort field)

### General Issues

```bash
# Check pod status
kubectl get pods -l app.kubernetes.io/name=syncnow

# Check service status
kubectl get svc -l app.kubernetes.io/name=syncnow

# Check logs
kubectl logs -l app.kubernetes.io/component=server
kubectl logs -l app.kubernetes.io/component=web

# Describe problematic resources
kubectl describe pod POD_NAME
kubectl describe svc SERVICE_NAME
```

## 💡 Best Practices

### For Production

1. **Use LoadBalancer services** for reliable external access
2. **Reserve static IPs** to prevent IP changes
3. **Configure proper resource limits** in values files
4. **Enable monitoring** and logging
5. **Use secrets** for sensitive configuration

### For Development

1. **Use NodePort or port-forwarding** to save costs
2. **Disable autoscaling** for predictable resource usage
3. **Use smaller resource requests** to fit on smaller clusters
4. **Enable debug logging** for troubleshooting

### Security Considerations

1. **Restrict access** using network policies or firewall rules
2. **Use HTTPS** in production (requires certificates)
3. **Change default passwords** and secrets
4. **Regularly update** container images

## 📋 Service Ports Reference

| Service | LoadBalancer Port | NodePort | Purpose |
|---------|------------------|----------|---------|
| Web App | 3000 | 30300 | Frontend UI |
| Envoy Proxy | 8080 | 30080 | gRPC-Web API |
| Server (direct) | 8090 | 30090 | gRPC API (internal) |

## 🔄 Updating Configuration

### Update Web App API URLs

After getting external IPs, update the web app configuration:

```bash
# For LoadBalancer
ENVOY_IP=$(kubectl get svc syncnow-envoy -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
kubectl patch deployment syncnow-web -p "{\"spec\":{\"template\":{\"spec\":{\"containers\":[{\"name\":\"web\",\"env\":[{\"name\":\"NEXT_PUBLIC_API_URL\",\"value\":\"http://$ENVOY_IP:8080\"}]}]}}}}"

# For NodePort
NODE_IP=$(kubectl get nodes -o jsonpath='{.items[0].status.addresses[?(@.type=="ExternalIP")].address}')
kubectl patch deployment syncnow-web -p "{\"spec\":{\"template\":{\"spec\":{\"containers\":[{\"name\":\"web\",\"env\":[{\"name\":\"NEXT_PUBLIC_API_URL\",\"value\":\"http://$NODE_IP:30080\"}]}]}}}}"
```

This guide provides multiple options for deploying SyncNow without domain names, suitable for internal use cases.
