# SyncNow Deployment to Local Kind Cluster

This guide explains how to deploy SyncNow to a local Kind (Kubernetes in Docker) cluster for development and testing.

## 🛠️ Prerequisites

### 1. Install Required Tools

```bash
# Install Docker (if not already installed)
# Follow instructions at: https://docs.docker.com/get-docker/

# Install Kind
curl -Lo ./kind https://kind.sigs.k8s.io/dl/v0.20.0/kind-linux-amd64
chmod +x ./kind
sudo mv ./kind /usr/local/bin/kind

# Install kubectl
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
chmod +x kubectl
sudo mv kubectl /usr/local/bin/

# Install Helm
curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash

# Verify installations
kind version
kubectl version --client
helm version
```

### 2. Build SyncNow Docker Images

```bash
# Navigate to project root
cd /path/to/syncnow-project

# Build the main SyncNow image
docker build -t syncnow:local .

# Build the web app image
docker build -t syncnow-web:local ./web

# Verify images
docker images | grep syncnow
```

## 🚀 Kind Cluster Setup

### 1. Create Kind Cluster Configuration

Create a Kind cluster configuration file:

```bash
cat > kind-config.yaml << EOF
kind: Cluster
apiVersion: kind.x-k8s.io/v1alpha4
name: syncnow-cluster
nodes:
- role: control-plane
  kubeadmConfigPatches:
  - |
    kind: InitConfiguration
    nodeRegistration:
      kubeletExtraArgs:
        node-labels: "ingress-ready=true"
  extraPortMappings:
  - containerPort: 80
    hostPort: 80
    protocol: TCP
  - containerPort: 443
    hostPort: 443
    protocol: TCP
  - containerPort: 30300
    hostPort: 30300
    protocol: TCP
  - containerPort: 30080
    hostPort: 30080
    protocol: TCP
- role: worker
- role: worker
EOF
```

### 2. Create and Configure Cluster

```bash
# Create the Kind cluster
kind create cluster --config kind-config.yaml

# Verify cluster is running
kubectl cluster-info --context kind-syncnow-cluster

# Load Docker images into Kind cluster
kind load docker-image syncnow:local --name syncnow-cluster
kind load docker-image syncnow-web:local --name syncnow-cluster

# Verify images are loaded
docker exec -it syncnow-cluster-control-plane crictl images | grep syncnow
```

## 📦 Deploy SyncNow with Helm

### 1. Create Local Values File

Create a values file optimized for Kind deployment:

```bash
cd deployments/helm/syncnow

cat > values-kind.yaml << EOF
# Kind-specific values for SyncNow deployment

# Global configuration
global:
  imageRegistry: ""
  imagePullSecrets: []

# Use local images
image:
  registry: ""
  repository: syncnow
  tag: "local"
  pullPolicy: Never  # Use local images

# Server configuration
server:
  enabled: true
  replicaCount: 1
  
  image:
    repository: syncnow
    tag: "local"
    pullPolicy: Never
  
  service:
    type: NodePort
    port: 8090
    targetPort: 8090
    nodePort: 30090
  
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 250m
      memory: 256Mi
  
  # Disable authentication for local testing
  auth:
    enabled: false

# Worker configuration
worker:
  enabled: true
  replicaCount: 1
  
  image:
    repository: syncnow
    tag: "local"
    pullPolicy: Never
  
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 250m
      memory: 256Mi

# Web app configuration
web:
  enabled: true
  replicaCount: 1
  
  image:
    repository: syncnow-web
    tag: "local"
    pullPolicy: Never
  
  service:
    type: NodePort
    port: 3000
    targetPort: 3000
    nodePort: 30300
  
  resources:
    limits:
      cpu: 250m
      memory: 256Mi
    requests:
      cpu: 100m
      memory: 128Mi
  
  env:
    NODE_ENV: "development"
    NEXT_PUBLIC_API_URL: "http://localhost:30080"
    NEXT_PUBLIC_GRPC_WEB_URL: "http://localhost:30080"

# Envoy proxy configuration
envoy:
  enabled: true
  replicaCount: 1
  
  service:
    type: NodePort
    port: 8080
    targetPort: 8080
    nodePort: 30080
  
  resources:
    limits:
      cpu: 250m
      memory: 256Mi
    requests:
      cpu: 100m
      memory: 128Mi

# Use built-in PostgreSQL with minimal resources
postgresql:
  enabled: true
  auth:
    postgresPassword: "postgres"
    username: "syncnow"
    password: "syncnow"
    database: "syncnow"
  primary:
    persistence:
      enabled: false  # Use emptyDir for local testing
    resources:
      limits:
        cpu: 250m
        memory: 256Mi
      requests:
        cpu: 100m
        memory: 128Mi

# Disable Temporal for simplicity (can be enabled if needed)
temporal:
  enabled: false

# Disable external services
externalDatabase:
  enabled: false

# Minimal monitoring
monitoring:
  enabled: false

# Disable network policies for local testing
networkPolicy:
  enabled: false

# Disable pod disruption budget for local testing
podDisruptionBudget:
  enabled: false
EOF
```

### 2. Deploy with Helm

```bash
# Update Helm dependencies
helm dependency update

# Install SyncNow
helm install syncnow . -f values-kind.yaml

# Wait for deployment to be ready
kubectl wait --for=condition=available --timeout=300s deployment -l app.kubernetes.io/name=syncnow

# Check deployment status
kubectl get pods -l app.kubernetes.io/name=syncnow
kubectl get services -l app.kubernetes.io/name=syncnow
```

## 🌐 Access the Application

### Service URLs

After deployment, access the services via NodePort:

```bash
# Web Application
echo "Web App: http://localhost:30300"

# API Endpoint (via Envoy)
echo "API: http://localhost:30080"

# Direct Server Access
echo "Server: http://localhost:30090"

# Check service endpoints
kubectl get svc -l app.kubernetes.io/name=syncnow
```

### Port Forwarding (Alternative)

If NodePort doesn't work, use port forwarding:

```bash
# Forward web app
kubectl port-forward svc/syncnow-web 3000:3000 &

# Forward API (Envoy)
kubectl port-forward svc/syncnow-envoy 8080:8080 &

# Forward server directly
kubectl port-forward svc/syncnow-server 8090:8090 &

# Access via localhost
echo "Web App: http://localhost:3000"
echo "API: http://localhost:8080"
echo "Server: http://localhost:8090"
```

## 🔧 Development Workflow

### 1. Update Images

When you make code changes:

```bash
# Rebuild images
docker build -t syncnow:local .
docker build -t syncnow-web:local ./web

# Load new images into Kind
kind load docker-image syncnow:local --name syncnow-cluster
kind load docker-image syncnow-web:local --name syncnow-cluster

# Restart deployments to use new images
kubectl rollout restart deployment syncnow-server
kubectl rollout restart deployment syncnow-worker
kubectl rollout restart deployment syncnow-web
```

### 2. Update Configuration

```bash
# Update Helm deployment
helm upgrade syncnow . -f values-kind.yaml

# Or update specific values
helm upgrade syncnow . -f values-kind.yaml \
  --set server.replicaCount=2 \
  --set worker.replicaCount=2
```

## 🐛 Troubleshooting

### Common Issues

1. **Images not found**:
   ```bash
   # Ensure images are loaded into Kind
   kind load docker-image syncnow:local --name syncnow-cluster
   kind load docker-image syncnow-web:local --name syncnow-cluster
   ```

2. **Pods stuck in Pending**:
   ```bash
   # Check resource constraints
   kubectl describe pod <pod-name>
   kubectl top nodes
   ```

3. **Services not accessible**:
   ```bash
   # Check NodePort services
   kubectl get svc -l app.kubernetes.io/name=syncnow
   
   # Use port forwarding as alternative
   kubectl port-forward svc/syncnow-web 3000:3000
   ```

4. **Database connection issues**:
   ```bash
   # Check PostgreSQL pod
   kubectl logs -l app.kubernetes.io/name=postgresql
   
   # Test database connection
   kubectl exec -it deployment/syncnow-server -- /bin/sh
   ```

### Debugging Commands

```bash
# Check all resources
kubectl get all -l app.kubernetes.io/name=syncnow

# Check pod logs
kubectl logs -l app.kubernetes.io/component=server
kubectl logs -l app.kubernetes.io/component=worker
kubectl logs -l app.kubernetes.io/component=web

# Describe problematic pods
kubectl describe pod <pod-name>

# Check events
kubectl get events --sort-by=.metadata.creationTimestamp
```

## 🧹 Cleanup

### Remove Deployment

```bash
# Uninstall Helm release
helm uninstall syncnow

# Delete persistent volumes (if any)
kubectl delete pvc -l app.kubernetes.io/name=syncnow
```

### Remove Kind Cluster

```bash
# Delete the entire cluster
kind delete cluster --name syncnow-cluster

# Remove configuration file
rm kind-config.yaml values-kind.yaml
```

This setup provides a complete local Kubernetes environment for developing and testing SyncNow!
