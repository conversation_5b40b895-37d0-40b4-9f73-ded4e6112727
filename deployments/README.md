# SyncNow Deployment Options

This directory contains various deployment configurations for SyncNow - Database Migration and Synchronization Platform.

## 📁 Directory Structure

```
deployments/
├── docker/                 # Docker-based deployments
│   └── Dockerfile          # Production Docker image
└── helm/                   # Kubernetes Helm deployments
    └── syncnow/            # Complete Helm chart for Kubernetes
        ├── Chart.yaml      # Chart metadata and dependencies
        ├── values.yaml     # Default configuration values
        ├── values-production.yaml    # Production-optimized values
        ├── values-ip-based.yaml      # IP-based deployment (no domain)
        ├── values-nodeport.yaml      # NodePort service configuration
        ├── templates/      # Kubernetes resource templates
        ├── deploy.sh       # Automated deployment script
        ├── deploy-ip-based.sh        # IP-based deployment script
        ├── validate.sh     # Chart validation script
        └── *.md           # Documentation files
```

## 🚀 Deployment Methods

### 1. Kubernetes with Helm (Recommended)

**Best for**: Production deployments, scalable environments, cloud platforms

**Location**: `helm/syncnow/`

**Features**:
- Complete Kubernetes deployment
- Auto-scaling support
- High availability configuration
- Multiple environment support (dev, staging, production)
- IP-based deployment option (no domain required)
- Comprehensive monitoring and observability

**Quick Start**:
```bash
cd deployments/helm/syncnow

# Local Kind cluster deployment
./deploy-kind.sh --create-cluster

# Standard deployment
helm install syncnow . -f values.yaml

# Production deployment
helm install syncnow . -f values-production.yaml

# IP-based deployment (no domain needed)
./deploy-ip-based.sh
```

### 2. Docker Compose

**Best for**: Local development, testing, single-machine deployments

**Location**: `../docker-compose.yml` (root directory)

**Features**:
- Simple local development setup
- All services in containers
- Easy to start/stop
- Good for testing and development

**Quick Start**:
```bash
# From project root
docker-compose up -d
```

### 3. Docker Container

**Best for**: Custom deployments, integration with existing systems

**Location**: `docker/Dockerfile`

**Features**:
- Production-ready Docker image
- Minimal footprint
- Can be used with any orchestration system
- Suitable for custom deployment scenarios

**Quick Start**:
```bash
cd deployments/docker

# Build image
docker build -t syncnow:latest .

# Run container
docker run -d \
  -p 8090:8090 \
  -e DB_HOST=your-db-host \
  -e TEMPORAL_HOST=your-temporal-host \
  syncnow:latest
```

## 🎯 Choosing the Right Deployment

### Use Helm (Kubernetes) When:
- ✅ Deploying to production environments
- ✅ Need auto-scaling and high availability
- ✅ Using cloud platforms (GKE, EKS, AKS)
- ✅ Want comprehensive monitoring and observability
- ✅ Need multiple environment support
- ✅ Require advanced networking and security features

### Use Docker Compose When:
- ✅ Local development and testing
- ✅ Single-machine deployments
- ✅ Quick prototyping
- ✅ Learning and experimentation
- ✅ CI/CD testing environments

### Use Docker Container When:
- ✅ Custom orchestration systems
- ✅ Integration with existing infrastructure
- ✅ Minimal deployment requirements
- ✅ Edge computing scenarios
- ✅ Specific containerization needs

### Use Kind (Local Kubernetes) When:
- ✅ Local Kubernetes development and testing
- ✅ Testing Helm charts and Kubernetes manifests
- ✅ Learning Kubernetes concepts
- ✅ CI/CD pipeline testing
- ✅ Kubernetes feature development

## 🔧 Configuration Management

### Helm Chart Configuration
The Helm chart supports extensive configuration through values files:

- **`values.yaml`**: Default development configuration
- **`values-production.yaml`**: Production-optimized settings
- **`values-ip-based.yaml`**: IP-based deployment (no domain required)
- **`values-nodeport.yaml`**: NodePort services for on-premises

### Environment Variables
All deployment methods support configuration via environment variables:

```bash
# Database configuration
DB_HOST=your-database-host
DB_PORT=5432
DB_NAME=syncnow
DB_USER=syncnow
DB_PASSWORD=your-password

# Temporal configuration
TEMPORAL_HOST=your-temporal-host
TEMPORAL_PORT=7233
TEMPORAL_NAMESPACE=default

# Application configuration
LOG_LEVEL=info
SERVER_PORT=8090
```

## 📚 Documentation

### Helm Chart Documentation
- **[README.md](helm/syncnow/README.md)**: Complete Helm chart guide
- **[IP-BASED-DEPLOYMENT.md](helm/syncnow/IP-BASED-DEPLOYMENT.md)**: Deploy without domain names
- **[CONFIG-MANAGEMENT.md](helm/syncnow/CONFIG-MANAGEMENT.md)**: Configuration management guide
- **[REDIS-REMOVAL.md](helm/syncnow/REDIS-REMOVAL.md)**: Redis removal documentation

### Quick Reference
- **Kind Deployment**: `cd deployments/helm/syncnow && ./deploy-kind.sh --create-cluster`
- **Helm Deployment**: `cd deployments/helm/syncnow && ./deploy.sh`
- **IP-based Deployment**: `cd deployments/helm/syncnow && ./deploy-ip-based.sh`
- **Docker Compose**: `docker-compose up -d` (from project root)
- **Validation**: `cd deployments/helm/syncnow && ./validate.sh`

## 🛠️ Development Workflow

### Local Development
1. Use Docker Compose for local development
2. Test changes with Helm chart in local Kubernetes (minikube/kind)
3. Deploy to staging with Helm chart
4. Deploy to production with production values

### CI/CD Integration
```bash
# Validate Helm chart
cd deployments/helm/syncnow
./validate.sh

# Deploy to staging
helm upgrade --install syncnow-staging . -f values-staging.yaml

# Deploy to production
helm upgrade --install syncnow-prod . -f values-production.yaml
```

## 🔍 Troubleshooting

### Common Issues
1. **Helm chart validation fails**: Run `./validate.sh` for detailed errors
2. **Pods not starting**: Check resource limits and node capacity
3. **Database connection issues**: Verify database credentials and network policies
4. **Service not accessible**: Check ingress configuration and DNS settings

### Getting Help
- Check the specific deployment method's documentation
- Review logs: `kubectl logs -l app.kubernetes.io/name=syncnow`
- Validate configuration: `helm template syncnow . | kubectl apply --dry-run=client -f -`

This deployment structure provides flexibility to choose the right deployment method for your specific needs while maintaining consistency across environments.
