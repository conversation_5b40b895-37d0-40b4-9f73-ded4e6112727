# --- Build Stage ---
FROM node:18-alpine AS builder

# Install protoc for proto generation
RUN apk add --no-cache protobuf protobuf-dev

WORKDIR /app

# Copy package files from web directory
COPY web/package*.json ./

# Install dependencies
RUN npm ci

# Copy proto files (needed for proto generation)
COPY proto ../proto

# Copy web source code
COPY web/ .

# Generate proto files and build the application
RUN npm run build

# --- Production Stage ---
FROM node:18-alpine AS runner

WORKDIR /app

# Create non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy built application
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

CMD ["node", "server.js"]
